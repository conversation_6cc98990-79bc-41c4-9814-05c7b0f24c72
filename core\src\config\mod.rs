/*!
# Configuration Module

Contains all configuration related code for the bridge.
*/

use std::time::Duration;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

use crate::utils::{get_env, get_env_opt};

/// Chat-port service configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatPortConfig {
    /// WebSocket URL for receiving messages
    pub websocket_url: String,
    /// HTTP API base URL for sending messages
    pub api_base_url: String,
    /// Connection timeout in seconds
    pub connection_timeout_secs: u64,
    /// Reconnection delay in seconds
    pub reconnect_delay_secs: u64,
    /// Maximum reconnection attempts
    pub max_reconnect_attempts: u32,
    /// Heartbeat interval in seconds
    pub heartbeat_interval_secs: u64,
}

/// AI service (genuis) configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiServiceConfig {
    /// Base URL for AI service
    pub base_url: String,
    /// API key for authentication
    pub api_key: String,
    /// Request timeout in seconds
    pub timeout_secs: u64,
    /// Retry attempts for failed requests
    pub retry_attempts: u32,
    /// Retry delay in seconds
    pub retry_delay_secs: u64,
    /// Default temperature for AI requests
    pub default_temperature: Option<f32>,
    /// Default max tokens for AI requests
    pub default_max_tokens: Option<i32>,
}

/// Bridge service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BridgeConfig {
    /// Service name
    pub service_name: String,
    /// Service version
    pub version: String,
    /// Maximum concurrent message processing
    pub max_concurrent_messages: usize,
    /// Message processing timeout in seconds
    pub message_timeout_secs: u64,
    /// Rate limit: messages per minute
    pub rate_limit_per_minute: u32,
    /// Enable message deduplication
    pub enable_deduplication: bool,
    /// Deduplication window in seconds
    pub deduplication_window_secs: u64,
}

impl BridgeConfig {
    /// Get message timeout as Duration
    pub fn message_timeout(&self) -> Duration {
        Duration::from_secs(self.message_timeout_secs)
    }

    /// Get deduplication window as Duration
    pub fn deduplication_window(&self) -> Duration {
        Duration::from_secs(self.deduplication_window_secs)
    }
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthConfig {
    /// Health check server port
    pub port: u16,
    /// Health check interval in seconds
    pub check_interval_secs: u64,
    /// Timeout for health checks in seconds
    pub timeout_secs: u64,
    /// Circuit breaker configuration
    pub circuit_breaker: CircuitBreakerConfig,
    /// Health thresholds configuration
    pub thresholds: HealthThresholds,
    /// Alerting configuration
    pub alerting: AlertingConfig,
    /// Metrics collection configuration
    pub metrics: MetricsConfig,
    /// Health history retention configuration
    pub history: HistoryConfig,
}

/// Circuit breaker configuration for health checks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Number of consecutive failures before opening circuit
    pub failure_threshold: u32,
    /// Time to wait before attempting recovery (seconds)
    pub recovery_timeout_secs: u64,
    /// Number of successful checks needed to close circuit
    pub success_threshold: u32,
    /// Enable circuit breaker functionality
    pub enabled: bool,
}

/// Health threshold configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthThresholds {
    /// Response time threshold for warning status (milliseconds)
    pub response_time_warning_ms: u64,
    /// Response time threshold for critical status (milliseconds)
    pub response_time_critical_ms: u64,
    /// Memory usage threshold for warning (percentage)
    pub memory_warning_percent: f64,
    /// Memory usage threshold for critical (percentage)
    pub memory_critical_percent: f64,
    /// CPU usage threshold for warning (percentage)
    pub cpu_warning_percent: f64,
    /// CPU usage threshold for critical (percentage)
    pub cpu_critical_percent: f64,
    /// Queue utilization threshold for warning (percentage)
    pub queue_warning_percent: f64,
    /// Queue utilization threshold for critical (percentage)
    pub queue_critical_percent: f64,
}

/// Alerting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertingConfig {
    /// Enable alerting functionality
    pub enabled: bool,
    /// Minimum alert level to trigger notifications
    pub min_alert_level: AlertLevel,
    /// Rate limiting for alerts (minimum seconds between same alert)
    pub rate_limit_secs: u64,
    /// Webhook URLs for notifications
    pub webhook_urls: Vec<String>,
    /// Email configuration for notifications
    pub email: Option<EmailConfig>,
}

/// Alert severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum AlertLevel {
    Info,
    Warning,
    Critical,
    Emergency,
}

impl std::str::FromStr for AlertLevel {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "info" => Ok(AlertLevel::Info),
            "warning" => Ok(AlertLevel::Warning),
            "critical" => Ok(AlertLevel::Critical),
            "emergency" => Ok(AlertLevel::Emergency),
            _ => Err(format!("Invalid alert level: {}", s)),
        }
    }
}

impl std::fmt::Display for AlertLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AlertLevel::Info => write!(f, "INFO"),
            AlertLevel::Warning => write!(f, "WARNING"),
            AlertLevel::Critical => write!(f, "CRITICAL"),
            AlertLevel::Emergency => write!(f, "EMERGENCY"),
        }
    }
}

/// Email configuration for alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    /// SMTP server host
    pub smtp_host: String,
    /// SMTP server port
    pub smtp_port: u16,
    /// SMTP username
    pub username: String,
    /// SMTP password
    pub password: String,
    /// From email address
    pub from_email: String,
    /// To email addresses
    pub to_emails: Vec<String>,
    /// Use TLS encryption
    pub use_tls: bool,
}

/// Metrics collection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    /// Enable metrics collection
    pub enabled: bool,
    /// Metrics collection interval in seconds
    pub collection_interval_secs: u64,
    /// Enable detailed component metrics
    pub detailed_metrics: bool,
    /// Enable performance profiling
    pub enable_profiling: bool,
}

/// Health history configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoryConfig {
    /// Enable health history tracking
    pub enabled: bool,
    /// Maximum number of health records to keep
    pub max_records: usize,
    /// History retention period in hours
    pub retention_hours: u64,
    /// Enable trend analysis
    pub enable_trends: bool,
}

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// Chat-port service configuration
    pub chat_port: ChatPortConfig,
    /// AI service (genuis) configuration
    pub ai_service: AiServiceConfig,
    /// Bridge service configuration
    pub bridge: BridgeConfig,
    /// Health check configuration
    pub health: HealthConfig,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            chat_port: ChatPortConfig {
                websocket_url: "ws://localhost:8081/ws".into(),
                api_base_url: "http://localhost:8081/api".into(),
                connection_timeout_secs: 30,
                reconnect_delay_secs: 5,
                max_reconnect_attempts: 10,
                heartbeat_interval_secs: 30,
            },
            ai_service: AiServiceConfig {
                base_url: "http://localhost:8000/api/v1".to_string(),
                api_key: "wellbot-dev-key-2025".to_string(),
                timeout_secs: 60,
                retry_attempts: 3,
                retry_delay_secs: 2,
                default_temperature: Some(0.7),
                default_max_tokens: Some(4000i32),
            },
            bridge: BridgeConfig {
                service_name: "wellbot-bridge".to_string(),
                version: "0.1.0".to_string(),
                max_concurrent_messages: 10,
                message_timeout_secs: 30,
                rate_limit_per_minute: 60,
                enable_deduplication: true,
                deduplication_window_secs: 300,
            },
            health: HealthConfig {
                port: 3000,
                check_interval_secs: 30,
                timeout_secs: 10,
                circuit_breaker: CircuitBreakerConfig {
                    failure_threshold: 5,
                    recovery_timeout_secs: 60,
                    success_threshold: 3,
                    enabled: true,
                },
                thresholds: HealthThresholds {
                    response_time_warning_ms: 1000,
                    response_time_critical_ms: 5000,
                    memory_warning_percent: 80.0,
                    memory_critical_percent: 95.0,
                    cpu_warning_percent: 70.0,
                    cpu_critical_percent: 90.0,
                    queue_warning_percent: 75.0,
                    queue_critical_percent: 90.0,
                },
                alerting: AlertingConfig {
                    enabled: true,
                    min_alert_level: AlertLevel::Warning,
                    rate_limit_secs: 300,
                    webhook_urls: vec![],
                    email: None,
                },
                metrics: MetricsConfig {
                    enabled: true,
                    collection_interval_secs: 60,
                    detailed_metrics: true,
                    enable_profiling: false,
                },
                history: HistoryConfig {
                    enabled: true,
                    max_records: 1000,
                    retention_hours: 24,
                    enable_trends: true,
                },
            },
        }
    }
}

impl Config {
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }

    /// Load configuration from environment variables
    pub fn from_env() -> Result<Self> {
        // Load .env file if it exists
        if let Err(e) = dotenvy::dotenv() {
            warn!("No .env file found or error loading it: {}", e);
        }

        let config = Self {
            chat_port: ChatPortConfig {
                websocket_url: get_env("CHAT_PORT_WEBSOCKET_URL", "ws://localhost:8081/ws".into()),
                api_base_url: get_env("CHAT_PORT_API_URL", "http://localhost:8081/api".into()),
                connection_timeout_secs: get_env("CHAT_PORT_CONNECTION_TIMEOUT", 30),
                reconnect_delay_secs: get_env("CHAT_PORT_RECONNECT_DELAY", 5),
                max_reconnect_attempts: get_env("CHAT_PORT_MAX_RECONNECTS", 10),
                heartbeat_interval_secs: get_env("CHAT_PORT_HEARTBEAT_INTERVAL", 30),
            },
            ai_service: AiServiceConfig {
                base_url: get_env("AI_SERVICE_URL", "http://localhost:8000/api/v1".into()),
                api_key: get_env("AI_SERVICE_API_KEY", "wellbot-dev-key-2025".into()),
                timeout_secs: get_env("AI_SERVICE_TIMEOUT", 60),
                retry_attempts: get_env("AI_SERVICE_RETRY_ATTEMPTS", 3),
                retry_delay_secs: get_env("AI_SERVICE_RETRY_DELAY", 2),
                default_temperature: get_env_opt("AI_SERVICE_DEFAULT_TEMPERATURE"),
                default_max_tokens: get_env_opt("AI_SERVICE_DEFAULT_MAX_TOKENS"),
            },
            bridge: BridgeConfig {
                service_name: get_env("BRIDGE_SERVICE_NAME", "wellbot-bridge".into()),
                version: get_env("BRIDGE_VERSION", env!("CARGO_PKG_VERSION").into()),
                max_concurrent_messages: get_env("BRIDGE_MAX_CONCURRENT", 100),
                message_timeout_secs: get_env("BRIDGE_MESSAGE_TIMEOUT", 120),
                rate_limit_per_minute: get_env("BRIDGE_RATE_LIMIT", 60),
                enable_deduplication: get_env("BRIDGE_ENABLE_DEDUP", true),
                deduplication_window_secs: get_env("BRIDGE_DEDUP_WINDOW", 300),
            },
            health: HealthConfig {
                port: get_env("HEALTH_PORT", 3030),
                check_interval_secs: get_env("HEALTH_CHECK_INTERVAL", 30),
                timeout_secs: get_env("HEALTH_CHECK_TIMEOUT", 10),
                circuit_breaker: CircuitBreakerConfig {
                    failure_threshold: get_env("HEALTH_CB_FAILURE_THRESHOLD", 5),
                    recovery_timeout_secs: get_env("HEALTH_CB_RECOVERY_TIMEOUT", 60),
                    success_threshold: get_env("HEALTH_CB_SUCCESS_THRESHOLD", 3),
                    enabled: get_env("HEALTH_CB_ENABLED", true),
                },
                thresholds: HealthThresholds {
                    response_time_warning_ms: get_env("HEALTH_RESPONSE_WARNING_MS", 1000),
                    response_time_critical_ms: get_env("HEALTH_RESPONSE_CRITICAL_MS", 5000),
                    memory_warning_percent: get_env("HEALTH_MEMORY_WARNING_PCT", 80.0),
                    memory_critical_percent: get_env("HEALTH_MEMORY_CRITICAL_PCT", 95.0),
                    cpu_warning_percent: get_env("HEALTH_CPU_WARNING_PCT", 70.0),
                    cpu_critical_percent: get_env("HEALTH_CPU_CRITICAL_PCT", 90.0),
                    queue_warning_percent: get_env("HEALTH_QUEUE_WARNING_PCT", 75.0),
                    queue_critical_percent: get_env("HEALTH_QUEUE_CRITICAL_PCT", 90.0),
                },
                alerting: AlertingConfig {
                    enabled: get_env("HEALTH_ALERTING_ENABLED", true),
                    min_alert_level: get_env("HEALTH_MIN_ALERT_LEVEL", AlertLevel::Warning),
                    rate_limit_secs: get_env("HEALTH_ALERT_RATE_LIMIT", 300),
                    webhook_urls: get_env("HEALTH_WEBHOOK_URLS", String::new())
                        .split(',')
                        .filter(|s| !s.trim().is_empty())
                        .map(|s| s.trim().to_string())
                        .collect(),
                    email: None, // Email config loaded separately if needed
                },
                metrics: MetricsConfig {
                    enabled: get_env("HEALTH_METRICS_ENABLED", true),
                    collection_interval_secs: get_env("HEALTH_METRICS_INTERVAL", 60),
                    detailed_metrics: get_env("HEALTH_DETAILED_METRICS", true),
                    enable_profiling: get_env("HEALTH_ENABLE_PROFILING", false),
                },
                history: HistoryConfig {
                    enabled: get_env("HEALTH_HISTORY_ENABLED", true),
                    max_records: get_env("HEALTH_HISTORY_MAX_RECORDS", 1000),
                    retention_hours: get_env("HEALTH_HISTORY_RETENTION_HOURS", 24),
                    enable_trends: get_env("HEALTH_HISTORY_ENABLE_TRENDS", true),
                },
            },
        };

        config.validate()?;
        info!("Configuration loaded and validated successfully");

        Ok(config)
    }
}

