package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"

	"chatport-go/internal/client"
)

// SendRequest represents the request payload for sending messages
type SendRequest struct {
	Number  string `json:"number"`
	Message string `json:"message"`
}

// SendResponse represents the response for send operations
type SendResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// normalizePhoneNumber extracts the phone number from various formats
// Handles: "1234567890", "+1234567890", "<EMAIL>", "+<EMAIL>"
func normalizePhoneNumber(number string) string {
	// Remove @s.whatsapp.net suffix if present
	if strings.HasSuffix(number, "@s.whatsapp.net") {
		number = strings.TrimSuffix(number, "@s.whatsapp.net")
	}

	// Remove leading + if present (international format)
	if strings.HasPrefix(number, "+") {
		number = strings.TrimPrefix(number, "+")
	}

	return number
}

// SendHandler handles POST /api/send requests to send WhatsApp messages
func SendHandler(w http.ResponseWriter, r *http.Request) {
	var payload SendRequest

	// Parse request body
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		respondWithError(w, http.StatusBadRequest, "Invalid JSON payload")
		return
	}

	// Validate required fields
	if payload.Number == "" || payload.Message == "" {
		respondWithError(w, http.StatusBadRequest, "Number and message are required")
		return
	}

	// Normalize the phone number (remove @s.whatsapp.net if present)
	normalizedNumber := normalizePhoneNumber(payload.Number)

	log.Printf("Sending message to %s (normalized: %s): %s", payload.Number, normalizedNumber, payload.Message)

	// Check if client is initialized and connected
	if client.Client == nil {
		respondWithError(w, http.StatusServiceUnavailable, "WhatsApp client not initialized")
		return
	}

	if !client.Client.IsConnected() {
		respondWithError(w, http.StatusServiceUnavailable, "WhatsApp client not connected")
		return
	}

	// Send message using normalized number
	if err := client.Client.SendMessage(normalizedNumber, payload.Message); err != nil {
		log.Printf("Failed to send message to %s (normalized: %s): %v", payload.Number, normalizedNumber, err)
		respondWithError(w, http.StatusInternalServerError, "Failed to send message")
		return
	}

	// Success response
	response := SendResponse{
		Success: true,
		Message: "Message sent successfully",
	}

	respondWithJSON(w, http.StatusOK, response)
}

// Helper functions for consistent JSON responses
func respondWithJSON(w http.ResponseWriter, code int, payload interface{}) {
	response, err := json.Marshal(payload)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	w.Write(response)
}

func respondWithError(w http.ResponseWriter, code int, message string) {
	respondWithJSON(w, code, SendResponse{
		Success: false,
		Error:   message,
	})
}
