/*!
# Message Processor

Professional message processing service with JID filtering, rate limiting, and intelligent routing.
Handles WhatsApp message validation, filtering by allowed JIDs, and AI processing integration.
*/

use crate::{
    config::BridgeConfig,
    error::{BridgeError, BridgeResult},
    services::{
        ai_client::AiClient, chat_port_client::ChatPortClient,
        jid_authorization::JidAuthorizationService,
    },
    types::{ExplanationRequest, IncomingWhatsAppData, MessageContext, SendMessageRequest},
};
use governor::{Quota, RateLimiter};
use std::{
    collections::HashMap,
    num::NonZeroU32,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{Mutex, Semaphore};
use tracing::{debug, error, info, instrument, warn};

/// Professional message processor with JID filtering, rate limiting, and intelligent routing
#[derive(Debug)]
pub struct MessageProcessor {
    config: BridgeConfig,
    chat_port_client: Arc<ChatPortClient>,
    ai_client: Option<Arc<AiClient>>,
    jid_authorization: Arc<JidAuthorizationService>,
    rate_limiter: Arc<
        RateLimiter<
            governor::state::NotKeyed,
            governor::state::InMemoryState,
            governor::clock::QuantaClock,
        >,
    >,
    processing_semaphore: Arc<Semaphore>,
    message_cache: Arc<Mutex<HashMap<String, Instant>>>,
    processing_stats: Arc<Mutex<ProcessingStats>>,
}

impl MessageProcessor {
    /// Create a new message processor with JID filtering
    pub async fn new(
        config: BridgeConfig,
        chat_port_client: Arc<ChatPortClient>,
        jid_authorization: Arc<JidAuthorizationService>,
    ) -> Self {
        Self::new_with_ai_client(config, chat_port_client, None, jid_authorization).await
    }

    /// Create a new message processor with AI client integration
    pub async fn new_with_ai_client(
        config: BridgeConfig,
        chat_port_client: Arc<ChatPortClient>,
        ai_client: Option<Arc<AiClient>>,
        jid_authorization: Arc<JidAuthorizationService>,
    ) -> Self {
        // Create rate limiter (messages per minute)
        let quota = Quota::per_minute(
            NonZeroU32::new(config.rate_limit_per_minute).unwrap_or(NonZeroU32::new(60).unwrap()),
        );
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        // Create semaphore for concurrent processing limit
        let processing_semaphore = Arc::new(Semaphore::new(config.max_concurrent_messages));

        // Create message cache for deduplication
        let message_cache = Arc::new(Mutex::new(HashMap::new()));

        // Initialize processing stats
        let processing_stats = Arc::new(Mutex::new(ProcessingStats::new()));

        info!(
            "Message processor initialized - Rate limit: {}/min, Max concurrent: {}",
            config.rate_limit_per_minute, config.max_concurrent_messages
        );

        Self {
            config,
            chat_port_client,
            ai_client,
            jid_authorization,
            rate_limiter,
            processing_semaphore,
            message_cache,
            processing_stats,
        }
    }

    /// Add a JID to the allowed list
    pub async fn add_allowed_jid(&self, jid: String) -> BridgeResult<()> {
        self.jid_authorization.add_jid(jid, None).await?;
        Ok(())
    }

    /// Remove a JID from the allowed list
    pub async fn remove_allowed_jid(&self, jid: &str) -> BridgeResult<bool> {
        self.jid_authorization.remove_jid(jid).await
    }

    /// Get list of allowed JIDs
    pub async fn get_allowed_jids(&self) -> Vec<String> {
        self.jid_authorization.get_authorized_jids().await
    }

    /// Check if a JID is allowed
    pub async fn is_jid_allowed(&self, jid: &str) -> bool {
        self.jid_authorization.is_authorized(jid).await
    }

    /// Process incoming WhatsApp message with JID filtering
    #[instrument(skip(self, whatsapp_data), fields(from = whatsapp_data.from, message_id = whatsapp_data.message_id))]
    pub async fn process_message(&self, whatsapp_data: IncomingWhatsAppData) -> BridgeResult<()> {
        // Update stats
        {
            let mut stats = self.processing_stats.lock().await;
            stats.total_messages_received += 1;
        }

        // Check if JID is allowed
        if !self.is_jid_allowed(&whatsapp_data.from).await {
            info!(
                "Message from {} rejected - JID not in allowed list",
                whatsapp_data.from
            );
            {
                let mut stats = self.processing_stats.lock().await;
                stats.messages_rejected_jid += 1;
            }
            return Ok(()); // Silently ignore unauthorized JIDs
        }

        info!(
            "✅ Message from {} accepted - JID is authorized",
            whatsapp_data.from
        );

        // Check rate limit
        if self.rate_limiter.check().is_err() {
            warn!(
                "Rate limit exceeded for message processing from {}",
                whatsapp_data.from
            );
            {
                let mut stats = self.processing_stats.lock().await;
                stats.messages_rejected_rate_limit += 1;
            }
            return Err(Box::new(BridgeError::RateLimit(
                "Message processing rate limit exceeded".to_string(),
            )));
        }

        // Check for duplicate messages
        if self.config.enable_deduplication && self.is_duplicate_message(&whatsapp_data).await? {
            info!(
                "Duplicate message detected from {}, skipping processing",
                whatsapp_data.from
            );
            {
                let mut stats = self.processing_stats.lock().await;
                stats.messages_rejected_duplicate += 1;
            }
            return Ok(());
        }

        // Acquire processing semaphore
        let _permit =
            self.processing_semaphore.acquire().await.map_err(|_| {
                BridgeError::ServiceUnavailable("Processing queue full".to_string())
            })?;

        // Create message context for tracking
        let mut context = MessageContext::new(&whatsapp_data);
        context.start_processing();

        info!(
            "🔄 Processing message {} from {} (ID: {})",
            context.id, context.whatsapp_from, context.whatsapp_message_id
        );

        // Process with timeout
        let result = tokio::time::timeout(
            self.config.message_timeout(),
            self.process_message_internal(context),
        )
        .await;

        match result {
            Ok(Ok(mut context)) => {
                context.message_sent();
                if let Some(total_time) = context.total_processing_time_ms() {
                    info!(
                        "✅ Message {} processed successfully in {}ms",
                        context.id, total_time
                    );
                }
                {
                    let mut stats = self.processing_stats.lock().await;
                    stats.messages_processed_successfully += 1;
                }
                Ok(())
            }
            Ok(Err(e)) => {
                error!("❌ Message processing failed: {}", e);
                {
                    let mut stats = self.processing_stats.lock().await;
                    stats.messages_failed += 1;
                }
                self.send_error_response(&whatsapp_data, &e).await?;
                Err(e)
            }
            Err(_) => {
                error!("⏰ Message processing timeout");
                {
                    let mut stats = self.processing_stats.lock().await;
                    stats.messages_timeout += 1;
                }
                let timeout_error = BridgeError::Timeout("Message processing timeout".to_string());
                self.send_error_response(&whatsapp_data, &timeout_error)
                    .await?;
                Err(Box::new(timeout_error))
            }
        }
    }

    /// Internal message processing logic - placeholder for AI integration
    async fn process_message_internal(
        &self,
        context: MessageContext,
    ) -> BridgeResult<MessageContext> {
        // Validate message
        self.validate_message(&context)?;

        debug!("🔍 Processing message {} internally", context.id);

        // Generate AI-powered response
        let response_message = self.generate_response(&context).await?;

        // Send response via chat-port
        let send_request = SendMessageRequest {
            number: context.whatsapp_from.clone(),
            message: response_message,
        };

        debug!("📤 Sending response to WhatsApp for message {}", context.id);

        let send_response = self.chat_port_client.send_message(send_request).await?;

        if !send_response.success {
            return Err(Box::new(BridgeError::ChatPort {
                message: send_response
                    .error
                    .unwrap_or_else(|| "Unknown send error".to_string()),
            }));
        }

        info!("📨 Response sent successfully for message {}", context.id);
        Ok(context)
    }

    /// Generate AI-powered response - requires AI service to be available
    async fn generate_response(&self, context: &MessageContext) -> BridgeResult<String> {
        let message = context.original_message.trim();
        let sender_jid = &context.whatsapp_from;

        debug!("🧠 Generating AI response for message from {}", sender_jid);

        // Require AI client for response generation
        let ai_client = self.ai_client.as_ref().ok_or_else(|| {
            error!("❌ AI client not configured - cannot generate response");
            Box::new(BridgeError::Configuration {
                message: "AI client is required for response generation but not configured"
                    .to_string(),
            })
        })?;

        debug!("🤖 Requesting AI-powered response");

        let ai_request = ExplanationRequest {
            prompt: self.build_ai_prompt(message, sender_jid),
            temperature: Some(0.7),
            max_tokens: Some(500),
        };

        match ai_client.generate_explanation(ai_request).await {
            Ok(ai_response) => {
                info!("✅ AI response generated successfully for {}", sender_jid);
                Ok(ai_response.content)
            }
            Err(e) => {
                error!("🚨 AI service failed to generate response: {}", e);
                Err(Box::new(BridgeError::AiService {
                    message: format!("Failed to generate AI response: {}", e),
                    status: None,
                }))
            }
        }
    }

    /// Build contextual AI prompt for better responses
    fn build_ai_prompt(&self, message: &str, sender_jid: &str) -> String {
        format!(
            "You are Wellbot, an intelligent WhatsApp assistant.

Context:
- User JID: {}
- Message: {}
- System: Professional WhatsApp integration service

Instructions:
- Provide helpful, concise responses
- Maintain professional but friendly tone
- If asked about technical details, explain clearly
- For greetings, be welcoming
- For questions, provide informative answers
- Keep responses under 200 words

Respond naturally to the user's message:",
            sender_jid, message
        )
    }

    /// Validate incoming message
    fn validate_message(&self, context: &MessageContext) -> BridgeResult<()> {
        if context.original_message.trim().is_empty() {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Empty message content".to_string(),
            )));
        }

        if context.original_message.len() > 4000 {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Message too long (max 4000 characters)".to_string(),
            )));
        }

        if context.whatsapp_from.is_empty() {
            return Err(Box::new(BridgeError::InvalidMessage(
                "Missing sender information".to_string(),
            )));
        }

        Ok(())
    }

    /// Check if message is a duplicate
    async fn is_duplicate_message(
        &self,
        whatsapp_data: &IncomingWhatsAppData,
    ) -> BridgeResult<bool> {
        let mut cache = self.message_cache.lock().await;
        let now = Instant::now();
        let dedup_window = Duration::from_secs(self.config.deduplication_window_secs);

        // Clean up old entries
        cache.retain(|_, timestamp| now.duration_since(*timestamp) < dedup_window);

        // Create deduplication key (sender + message content hash)
        let dedup_key = format!(
            "{}:{}",
            whatsapp_data.from,
            whatsapp_data.message.chars().take(100).collect::<String>()
        );

        if cache.contains_key(&dedup_key) {
            return Ok(true);
        }

        // Add to cache
        cache.insert(dedup_key, now);
        Ok(false)
    }

    /// Send error response to WhatsApp user
    async fn send_error_response(
        &self,
        whatsapp_data: &IncomingWhatsAppData,
        error: &BridgeError,
    ) -> BridgeResult<()> {
        let error_message = match error {
            BridgeError::RateLimit(_) => {
                "I'm currently handling many requests. Please try again in a few minutes."
                    .to_string()
            }
            BridgeError::Timeout(_) => {
                "Your request is taking longer than expected. Please try again.".to_string()
            }
            BridgeError::InvalidMessage(_) => {
                "I couldn't process your message. Please make sure it's not empty and try again."
                    .to_string()
            }
            BridgeError::ServiceUnavailable(_) => {
                "I'm temporarily unavailable. Please try again later.".to_string()
            }
            _ => "I encountered an error processing your message. Please try again.".to_string(),
        };

        let send_request = SendMessageRequest {
            number: whatsapp_data.from.clone(),
            message: error_message,
        };

        match self.chat_port_client.send_message(send_request).await {
            Ok(_) => debug!("Error response sent to user"),
            Err(e) => warn!("Failed to send error response: {}", e),
        }

        Ok(())
    }

    /// Get comprehensive processing statistics
    pub async fn get_stats(&self) -> ProcessingStats {
        let cache = self.message_cache.lock().await;
        let stats = self.processing_stats.lock().await;
        let jid_stats = self.jid_authorization.get_stats().await;

        ProcessingStats {
            total_messages_received: stats.total_messages_received,
            messages_processed_successfully: stats.messages_processed_successfully,
            messages_failed: stats.messages_failed,
            messages_timeout: stats.messages_timeout,
            messages_rejected_jid: stats.messages_rejected_jid,
            messages_rejected_rate_limit: stats.messages_rejected_rate_limit,
            messages_rejected_duplicate: stats.messages_rejected_duplicate,
            cached_messages: cache.len(),
            available_permits: self.processing_semaphore.available_permits(),
            max_concurrent: self.config.max_concurrent_messages,
            allowed_jids_count: jid_stats.active_jids,
        }
    }

    /// Reset processing statistics
    pub async fn reset_stats(&self) {
        let mut stats = self.processing_stats.lock().await;
        *stats = ProcessingStats::new();
        info!("📊 Processing statistics reset");
    }
}

/// Comprehensive processing statistics
#[derive(Default, Debug, Clone)]
pub struct ProcessingStats {
    pub total_messages_received: u64,
    pub messages_processed_successfully: u64,
    pub messages_failed: u64,
    pub messages_timeout: u64,
    pub messages_rejected_jid: u64,
    pub messages_rejected_rate_limit: u64,
    pub messages_rejected_duplicate: u64,
    pub cached_messages: usize,
    pub available_permits: usize,
    pub max_concurrent: usize,
    pub allowed_jids_count: usize,
}

impl ProcessingStats {
    /// Create new empty statistics
    pub fn new() -> Self {
        Self {
            total_messages_received: 0,
            messages_processed_successfully: 0,
            messages_failed: 0,
            messages_timeout: 0,
            messages_rejected_jid: 0,
            messages_rejected_rate_limit: 0,
            messages_rejected_duplicate: 0,
            cached_messages: 0,
            available_permits: 0,
            max_concurrent: 0,
            allowed_jids_count: 0,
        }
    }

    /// Calculate success rate as percentage
    pub fn success_rate(&self) -> f64 {
        if self.total_messages_received == 0 {
            100.0
        } else {
            (self.messages_processed_successfully as f64 / self.total_messages_received as f64)
                * 100.0
        }
    }

    /// Calculate rejection rate as percentage
    pub fn rejection_rate(&self) -> f64 {
        if self.total_messages_received == 0 {
            0.0
        } else {
            let total_rejected = self.messages_rejected_jid
                + self.messages_rejected_rate_limit
                + self.messages_rejected_duplicate;
            (total_rejected as f64 / self.total_messages_received as f64) * 100.0
        }
    }
}
