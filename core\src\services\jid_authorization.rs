/*!
# JID Authorization Service

Comprehensive JID (WhatsApp account identifier) authorization system with persistent storage,
dynamic management, and integration with the bridge service for secure message processing.
*/

use std::{
    collections::HashMap,
    path::PathBuf,
    sync::Arc,
    time::{Duration, SystemTime, UNIX_EPOCH},
};

use serde::{Deserialize, Serialize};
use tokio::{
    fs,
    sync::{Mute<PERSON>, RwLock},
    time::interval,
};
use tracing::{debug, error, info, instrument, warn};

use crate::{
    error::{BridgeError, BridgeResult},
    utils::get_env,
};

/// JID authorization entry with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JidEntry {
    pub jid: String,
    pub display_name: Option<String>,
    pub added_at: u64,
    pub last_message_at: Option<u64>,
    pub message_count: u64,
    pub is_active: bool,
    pub notes: Option<String>,
}

impl JidEntry {
    /// Create a new JID entry
    pub fn new(jid: String, display_name: Option<String>) -> Self {
        Self {
            jid,
            display_name,
            added_at: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            last_message_at: None,
            message_count: 0,
            is_active: true,
            notes: None,
        }
    }

    /// Update message activity
    pub fn record_message(&mut self) {
        self.message_count += 1;
        self.last_message_at = Some(
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        );
    }
}

/// JID authorization configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JidAuthConfig {
    pub storage_path: PathBuf,
    pub auto_save_interval_secs: u64,
    pub backup_retention_days: u32,
    pub enable_activity_tracking: bool,
    pub max_inactive_days: Option<u32>,
}

impl JidAuthConfig {
    /// Create a new configuration from environment variables.
    pub fn from_env() -> BridgeResult<Self> {
        Ok(Self {
            storage_path: get_env(
                "JID_STORAGE_PATH",
                PathBuf::from("data/jid_authorization.json"),
            ),
            auto_save_interval_secs: std::env::var("JID_AUTO_SAVE_INTERVAL_SECS")
                .map(|s| s.parse().unwrap_or(300))
                .unwrap_or(300),
            backup_retention_days: std::env::var("JID_BACKUP_RETENTION_DAYS")
                .map(|s| s.parse().unwrap_or(30))
                .unwrap_or(30),
            enable_activity_tracking: std::env::var("JID_ENABLE_ACTIVITY_TRACKING")
                .map(|s| s.parse().unwrap_or(true))
                .unwrap_or(true),
            max_inactive_days: std::env::var("JID_MAX_INACTIVE_DAYS")
                .map(|s| s.parse::<u32>().ok())
                .ok()
                .flatten(),
        })
    }
}

impl Default for JidAuthConfig {
    fn default() -> Self {
        Self {
            storage_path: PathBuf::from("data/jid_authorization.json"),
            auto_save_interval_secs: 300, // 5 minutes
            backup_retention_days: 30,
            enable_activity_tracking: true,
            max_inactive_days: Some(90), // 3 months
        }
    }
}

/// JID authorization statistics
#[derive(Debug, Clone, Default)]
pub struct JidAuthStats {
    pub total_jids: usize,
    pub active_jids: usize,
    pub inactive_jids: usize,
    pub total_messages_processed: u64,
    pub last_save_time: Option<u64>,
    pub last_cleanup_time: Option<u64>,
}

/// Comprehensive JID authorization service
#[derive(Debug)]
pub struct JidAuthorizationService {
    config: JidAuthConfig,
    jid_entries: Arc<RwLock<HashMap<String, JidEntry>>>,
    stats: Arc<Mutex<JidAuthStats>>,
    auto_save_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
}

impl JidAuthorizationService {
    /// Create a new JID authorization service
    #[instrument(skip(config))]
    pub async fn new(config: JidAuthConfig) -> BridgeResult<Self> {
        info!("🔐 Initializing JID Authorization Service...");

        // Ensure storage directory exists
        if let Some(parent) = config.storage_path.parent() {
            fs::create_dir_all(parent).await.map_err(|e| {
                Box::new(BridgeError::ServiceUnavailable(format!(
                    "Failed to create storage directory: {}",
                    e
                )))
            })?;
        }

        let service = Self {
            config,
            jid_entries: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(Mutex::new(JidAuthStats::default())),
            auto_save_handle: Arc::new(Mutex::new(None)),
        };

        // Load existing data
        service.load_from_storage().await?;

        // Start auto-save if configured
        if service.config.auto_save_interval_secs > 0 {
            service.start_auto_save().await;
        }

        info!("✅ JID Authorization Service initialized");
        Ok(service)
    }

    /// Add a JID to the authorized list
    #[instrument(skip(self))]
    pub async fn add_jid(&self, jid: String, display_name: Option<String>) -> BridgeResult<bool> {
        info!("➕ Adding JID to authorized list: {}", jid);

        let mut entries = self.jid_entries.write().await;
        let was_new = if let Some(existing) = entries.get_mut(&jid) {
            // Update existing entry
            existing.is_active = true;
            if let Some(name) = display_name {
                existing.display_name = Some(name);
            }
            false
        } else {
            // Create new entry
            let entry = JidEntry::new(jid.clone(), display_name);
            entries.insert(jid.clone(), entry);
            true
        };

        // Update stats
        self.update_stats().await;

        if was_new {
            info!("✅ New JID added: {}", jid);
        } else {
            info!("✅ Existing JID reactivated: {}", jid);
        }

        Ok(was_new)
    }

    /// Remove a JID from the authorized list
    #[instrument(skip(self))]
    pub async fn remove_jid(&self, jid: &str) -> BridgeResult<bool> {
        info!("➖ Removing JID from authorized list: {}", jid);

        let mut entries = self.jid_entries.write().await;
        let was_removed = if let Some(entry) = entries.get_mut(jid) {
            entry.is_active = false;
            true
        } else {
            false
        };

        // Update stats
        self.update_stats().await;

        if was_removed {
            info!("✅ JID deactivated: {}", jid);
        } else {
            warn!("JID not found in authorized list: {}", jid);
        }

        Ok(was_removed)
    }

    /// Check if a JID is authorized
    #[instrument(skip(self))]
    pub async fn is_authorized(&self, jid: &str) -> bool {
        let entries = self.jid_entries.read().await;
        entries
            .get(jid)
            .map(|entry| entry.is_active)
            .unwrap_or(false)
    }

    /// Get all authorized JIDs
    pub async fn get_authorized_jids(&self) -> Vec<String> {
        let entries = self.jid_entries.read().await;
        entries
            .values()
            .filter(|entry| entry.is_active)
            .map(|entry| entry.jid.clone())
            .collect()
    }

    /// Get all JID entries with metadata
    pub async fn get_all_entries(&self) -> Vec<JidEntry> {
        let entries = self.jid_entries.read().await;
        entries.values().cloned().collect()
    }

    /// Record message activity for a JID
    #[instrument(skip(self))]
    pub async fn record_message_activity(&self, jid: &str) -> BridgeResult<()> {
        if !self.config.enable_activity_tracking {
            return Ok(());
        }

        let mut entries = self.jid_entries.write().await;
        if let Some(entry) = entries.get_mut(jid) {
            entry.record_message();
            debug!("📊 Recorded message activity for JID: {}", jid);
        }

        // Update global stats
        {
            let mut stats = self.stats.lock().await;
            stats.total_messages_processed += 1;
        }

        Ok(())
    }

    /// Get authorization statistics
    pub async fn get_stats(&self) -> JidAuthStats {
        self.stats.lock().await.clone()
    }

    /// Update internal statistics
    async fn update_stats(&self) {
        let entries = self.jid_entries.read().await;
        let mut stats = self.stats.lock().await;

        stats.total_jids = entries.len();
        stats.active_jids = entries.values().filter(|e| e.is_active).count();
        stats.inactive_jids = entries.values().filter(|e| !e.is_active).count();
    }

    /// Save JID data to persistent storage
    #[instrument(skip(self))]
    pub async fn save_to_storage(&self) -> BridgeResult<()> {
        debug!("💾 Saving JID authorization data to storage");

        let entries = self.jid_entries.read().await;
        let entries_vec: Vec<JidEntry> = entries.values().cloned().collect();

        let json_data = serde_json::to_string_pretty(&entries_vec).map_err(|e| {
            Box::new(BridgeError::SerializationError(format!(
                "Failed to serialize JID data: {}",
                e
            )))
        })?;

        // Create backup if file exists
        if self.config.storage_path.exists() {
            let backup_path = self.config.storage_path.with_extension("json.bak");
            if let Err(e) = fs::copy(&self.config.storage_path, &backup_path).await {
                warn!("Failed to create backup: {}", e);
            }
        }

        fs::write(&self.config.storage_path, json_data)
            .await
            .map_err(|e| {
                Box::new(BridgeError::ServiceUnavailable(format!(
                    "Failed to write JID data to storage: {}",
                    e
                )))
            })?;

        // Update save time
        {
            let mut stats = self.stats.lock().await;
            stats.last_save_time = Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            );
        }

        info!("✅ JID authorization data saved to storage");
        Ok(())
    }

    /// Load JID data from persistent storage
    #[instrument(skip(self))]
    async fn load_from_storage(&self) -> BridgeResult<()> {
        if !self.config.storage_path.exists() {
            info!("📂 No existing JID authorization data found, starting fresh");
            return Ok(());
        }

        debug!("📂 Loading JID authorization data from storage");

        let json_data = fs::read_to_string(&self.config.storage_path)
            .await
            .map_err(|e| {
                Box::new(BridgeError::ServiceUnavailable(format!(
                    "Failed to read JID data from storage: {}",
                    e
                )))
            })?;

        let entries_vec: Vec<JidEntry> = serde_json::from_str(&json_data).map_err(|e| {
            Box::new(BridgeError::SerializationError(format!(
                "Failed to deserialize JID data: {}",
                e
            )))
        })?;

        // Load entries into HashMap
        {
            let mut entries = self.jid_entries.write().await;
            entries.clear();
            for entry in entries_vec {
                entries.insert(entry.jid.clone(), entry);
            }
        }

        // Update stats
        self.update_stats().await;

        let stats = self.stats.lock().await;
        info!(
            "✅ Loaded {} JID entries from storage ({} active, {} inactive)",
            stats.total_jids, stats.active_jids, stats.inactive_jids
        );

        Ok(())
    }

    /// Start auto-save background task
    async fn start_auto_save(&self) {
        let interval_duration = Duration::from_secs(self.config.auto_save_interval_secs);
        let service = Arc::new(self.clone());

        let handle = tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            info!(
                "🔄 Auto-save task started (interval: {}s)",
                interval_duration.as_secs()
            );

            loop {
                interval.tick().await;

                if let Err(e) = service.save_to_storage().await {
                    error!("Auto-save failed: {}", e);
                }
            }
        });

        *self.auto_save_handle.lock().await = Some(handle);
    }

    /// Stop auto-save background task
    pub async fn stop_auto_save(&self) {
        if let Some(handle) = self.auto_save_handle.lock().await.take() {
            handle.abort();
            info!("🛑 Auto-save task stopped");
        }
    }

    /// Cleanup inactive JIDs based on configuration
    #[instrument(skip(self))]
    pub async fn cleanup_inactive_jids(&self) -> BridgeResult<usize> {
        let max_inactive_days = match self.config.max_inactive_days {
            Some(days) => days,
            None => return Ok(0), // Cleanup disabled
        };

        info!(
            "🧹 Starting cleanup of inactive JIDs (max age: {} days)",
            max_inactive_days
        );

        let cutoff_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs()
            - (max_inactive_days as u64 * 24 * 60 * 60);

        let mut entries = self.jid_entries.write().await;
        let mut removed_count = 0;

        entries.retain(|jid, entry| {
            let should_keep =
                entry.is_active || entry.last_message_at.unwrap_or(entry.added_at) > cutoff_time;

            if !should_keep {
                info!("🗑️ Removing inactive JID: {}", jid);
                removed_count += 1;
            }

            should_keep
        });

        // Update cleanup time
        {
            let mut stats = self.stats.lock().await;
            stats.last_cleanup_time = Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            );
        }

        // Update stats
        self.update_stats().await;

        info!(
            "✅ Cleanup completed, removed {} inactive JIDs",
            removed_count
        );
        Ok(removed_count)
    }

    /// Graceful shutdown
    pub async fn shutdown(&self) -> BridgeResult<()> {
        info!("🛑 Shutting down JID Authorization Service...");

        // Stop auto-save
        self.stop_auto_save().await;

        // Final save
        self.save_to_storage().await?;

        info!("✅ JID Authorization Service shutdown complete");
        Ok(())
    }
}

// Implement Clone for JidAuthorizationService to enable Arc sharing
impl Clone for JidAuthorizationService {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jid_entries: self.jid_entries.clone(),
            stats: self.stats.clone(),
            auto_save_handle: self.auto_save_handle.clone(),
        }
    }
}
