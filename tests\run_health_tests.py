#!/usr/bin/env python3
"""
Simple test runner for Wellbot Bridge Health Endpoint Tests

This script provides easy-to-use commands for running health endpoint tests
with different configurations and scenarios.

Usage Examples:
    python run_health_tests.py                    # Basic tests
    python run_health_tests.py --verbose          # Verbose output
    python run_health_tests.py --load-test        # Include load testing
    python run_health_tests.py --save-report      # Save detailed report
    python run_health_tests.py --stress-test 30   # 30-second stress test
"""

import sys
import subprocess
import argparse
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import requests
        print("✅ requests library is available")
        return True
    except ImportError:
        print("❌ requests library not found")
        print("Install with: pip install requests")
        return False


def check_service_status(base_url="http://localhost:3030"):
    """Check if the Wellbot Bridge Service is running"""
    try:
        import requests
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Service is running at {base_url} (status: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ Service not reachable at {base_url}: {e}")
        print("Make sure the Wellbot Bridge Service is running:")
        print("  cd core && cargo run --bin wellbot-bridge")
        return False


def run_basic_tests(args):
    """Run basic health endpoint tests"""
    cmd = [sys.executable, "health_endpoint_tests.py"]
    
    if args.base_url:
        cmd.extend(["--base-url", args.base_url])
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_load_tests(args):
    """Run load tests"""
    cmd = [sys.executable, "health_endpoint_tests.py", "--load-test"]
    
    if args.base_url:
        cmd.extend(["--base-url", args.base_url])
    if args.concurrent:
        cmd.extend(["--concurrent", str(args.concurrent)])
    if args.requests:
        cmd.extend(["--requests", str(args.requests)])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")
    
    print(f"🔥 Running load test: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def run_stress_tests(args):
    """Run stress tests"""
    cmd = [sys.executable, "health_endpoint_tests.py", "--stress-test", str(args.stress_test)]
    
    if args.base_url:
        cmd.extend(["--base-url", args.base_url])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")
    
    print(f"💪 Running stress test: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge Health Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_health_tests.py                    # Basic health tests
  python run_health_tests.py --verbose          # Verbose output
  python run_health_tests.py --load-test        # Include load testing
  python run_health_tests.py --save-report      # Save detailed report
  python run_health_tests.py --stress-test 30   # 30-second stress test
  
  # Custom service URL
  python run_health_tests.py --base-url http://localhost:8080
  
  # Load test with custom parameters
  python run_health_tests.py --load-test --concurrent 20 --requests 200
        """
    )
    
    parser.add_argument("--base-url", default="http://localhost:3030",
                       help="Base URL of the service (default: http://localhost:3030)")
    parser.add_argument("--timeout", type=float, default=10.0,
                       help="Request timeout in seconds (default: 10.0)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")
    parser.add_argument("--save-report", action="store_true",
                       help="Save detailed report to JSON file")
    
    # Test type options
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument("--load-test", action="store_true",
                           help="Run load test after basic tests")
    test_group.add_argument("--stress-test", type=int, metavar="SECONDS",
                           help="Run stress test for specified seconds")
    
    # Load test specific options
    parser.add_argument("--concurrent", type=int, default=10,
                       help="Concurrent requests for load test (default: 10)")
    parser.add_argument("--requests", type=int, default=100,
                       help="Total requests for load test (default: 100)")
    
    # Additional options
    parser.add_argument("--skip-checks", action="store_true",
                       help="Skip dependency and service checks")
    
    args = parser.parse_args()
    
    print("🌉 Wellbot Bridge Health Test Runner")
    print("=" * 50)
    
    # Check dependencies and service status
    if not args.skip_checks:
        if not check_dependencies():
            return 1
        
        if not check_service_status(args.base_url):
            return 1
    
    # Run appropriate tests
    try:
        if args.stress_test:
            result = run_stress_tests(args)
        elif args.load_test:
            result = run_load_tests(args)
        else:
            result = run_basic_tests(args)
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
