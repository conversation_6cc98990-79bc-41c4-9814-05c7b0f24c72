#!/usr/bin/env python3
"""
Comprehensive WebSocket Connection Tests for Wellbot Bridge Service

This module provides manual testing capabilities for WebSocket connectivity,
message handling, subscription management, and connection lifecycle using
Python websockets library.

Usage:
    python websocket_connection_tests.py
    python websocket_connection_tests.py --chat-port-url ws://localhost:8081/ws
    python websocket_connection_tests.py --verbose --timeout 30
"""

import argparse
import asyncio
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import websockets
from dataclasses import dataclass
from enum import Enum
import uuid
import logging


class TestResult(Enum):
    """Test result status"""

    PASS = "PASS"
    FAIL = "FAIL"
    WARN = "WARN"
    SKIP = "SKIP"


@dataclass
class WebSocketTestCase:
    """WebSocket test case definition"""

    name: str
    description: str
    test_func: Callable
    timeout: float = 30.0
    expected_messages: Optional[List[str]] = None
    setup_func: Optional[Callable] = None
    cleanup_func: Optional[Callable] = None


@dataclass
class WebSocketTestReport:
    """WebSocket test execution report"""

    test_name: str
    result: TestResult
    duration_ms: float
    messages_sent: int
    messages_received: int
    error_message: Optional[str]
    received_messages: List[Dict]
    timestamp: datetime


class WebSocketTester:
    """Comprehensive WebSocket testing suite"""

    def __init__(
        self,
        chat_port_url: str = "ws://localhost:8081/ws",
        bridge_url: str = "ws://localhost:3030/ws",
        timeout: float = 30.0,
        verbose: bool = False,
    ):
        self.chat_port_url = chat_port_url
        self.bridge_url = bridge_url
        self.timeout = timeout
        self.verbose = verbose
        self.test_reports: List[WebSocketTestReport] = []
        self.client_id = f"test-client-{uuid.uuid4().hex[:8]}"

        # Configure logging
        log_level = logging.DEBUG if verbose else logging.INFO
        logging.basicConfig(
            level=log_level,
            format="[%(asctime)s] [%(levelname)s] %(message)s",
            datefmt="%H:%M:%S",
        )
        self.logger = logging.getLogger(__name__)

    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp and color coding"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if level == "ERROR":
            print(f"\033[91m[{timestamp}] [FAIL] {message}\033[0m")
        elif level == "WARN":
            print(f"\033[93m[{timestamp}] [WARN] {message}\033[0m")
        elif level == "PASS":
            print(f"\033[92m[{timestamp}] [PASS] {message}\033[0m")
        elif level == "INFO":
            print(f"\033[94m[{timestamp}] [INFO] {message}\033[0m")
        else:
            print(f"[{timestamp}] [{level}] {message}")

    async def create_websocket_message(self, msg_type: str, data: Any = None) -> str:
        """Create a properly formatted WebSocket message"""
        message = {
            "type": msg_type,
            "timestamp": datetime.now().isoformat() + "Z",
            "id": str(uuid.uuid4()),
        }

        if data is not None:
            message["data"] = data

        return json.dumps(message)

    async def create_subscription_message(self, action: str, subscription: str) -> str:
        """Create a subscription/unsubscription message"""
        data = {"action": action, "subscription": subscription}
        return await self.create_websocket_message("subscribe", data)

    async def test_basic_connection(self, websocket_url: str) -> WebSocketTestReport:
        """Test basic WebSocket connection establishment"""
        start_time = time.time()
        messages_received = []

        try:
            self.log(f"Testing basic connection to {websocket_url}")

            async with websockets.connect(websocket_url) as websocket:
                self.log("✅ WebSocket connection established")

                # Wait for welcome message
                try:
                    welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    welcome_data = json.loads(welcome_msg)
                    messages_received.append(welcome_data)

                    if welcome_data.get("type") == "welcome":
                        self.log("✅ Received welcome message")
                        if self.verbose:
                            self.log(
                                f"Welcome data: {json.dumps(welcome_data, indent=2)}"
                            )
                    else:
                        self.log(
                            f"⚠️ Expected welcome message, got: {welcome_data.get('type')}"
                        )

                except asyncio.TimeoutError:
                    self.log("⚠️ No welcome message received within 5 seconds")

                # Test connection close
                await websocket.close()
                self.log("✅ Connection closed gracefully")

                return WebSocketTestReport(
                    test_name="Basic Connection",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_sent=0,
                    messages_received=len(messages_received),
                    error_message=None,
                    received_messages=messages_received,
                    timestamp=datetime.now(),
                )

        except asyncio.TimeoutError:
            return WebSocketTestReport(
                test_name="Basic Connection",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=0,
                messages_received=len(messages_received),
                error_message="Connection timeout",
                received_messages=messages_received,
                timestamp=datetime.now(),
            )
        except Exception as e:
            return WebSocketTestReport(
                test_name="Basic Connection",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=0,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_subscription_mechanism(
        self, websocket_url: str
    ) -> WebSocketTestReport:
        """Test WebSocket subscription and unsubscription"""
        start_time = time.time()
        messages_sent = 0
        messages_received = []

        try:
            self.log("Testing subscription mechanism")

            async with websockets.connect(websocket_url) as websocket:
                # Wait for welcome message
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                messages_received.append(json.loads(welcome_msg))

                # Test subscription to WhatsApp messages
                subscription_types = ["whatsapp", "status", "metrics", "health", "all"]

                for sub_type in subscription_types:
                    # Subscribe
                    sub_msg = await self.create_subscription_message(
                        "subscribe", sub_type
                    )
                    await websocket.send(sub_msg)
                    messages_sent += 1
                    self.log(f"📡 Subscribed to {sub_type}")

                    # Wait a bit for any response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        try:
                            response_data = json.loads(response)
                            messages_received.append(response_data)
                            if self.verbose:
                                self.log(
                                    f"Response: {response_data.get('type', 'unknown')}"
                                )
                        except json.JSONDecodeError as e:
                            if self.verbose:
                                self.log(
                                    f"JSON decode error: {e}, raw response: {response[:100]}..."
                                )
                    except asyncio.TimeoutError:
                        # No immediate response is normal for subscriptions
                        pass

                    # Unsubscribe
                    unsub_msg = await self.create_subscription_message(
                        "unsubscribe", sub_type
                    )
                    await websocket.send(unsub_msg)
                    messages_sent += 1
                    self.log(f"📡 Unsubscribed from {sub_type}")

                return WebSocketTestReport(
                    test_name="Subscription Mechanism",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_sent=messages_sent,
                    messages_received=len(messages_received),
                    error_message=None,
                    received_messages=messages_received,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return WebSocketTestReport(
                test_name="Subscription Mechanism",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_heartbeat_mechanism(self, websocket_url: str) -> WebSocketTestReport:
        """Test WebSocket heartbeat/ping-pong mechanism"""
        start_time = time.time()
        messages_sent = 0
        messages_received = []

        try:
            self.log("Testing heartbeat mechanism")

            async with websockets.connect(websocket_url) as websocket:
                # Wait for welcome message
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                messages_received.append(json.loads(welcome_msg))

                # Send heartbeat message
                heartbeat_msg = await self.create_websocket_message(
                    "heartbeat",
                    {
                        "client_id": self.client_id,
                        "timestamp": datetime.now().isoformat() + "Z",
                    },
                )
                await websocket.send(heartbeat_msg)
                messages_sent += 1
                self.log("💓 Sent heartbeat message")

                # Wait for any response or ping from server
                try:
                    for _ in range(3):  # Wait for up to 3 messages
                        response = await asyncio.wait_for(
                            websocket.recv(), timeout=10.0
                        )
                        response_data = json.loads(response)
                        messages_received.append(response_data)

                        msg_type = response_data.get("type")
                        if msg_type == "ping":
                            self.log("🏓 Received ping from server")
                            # Send pong response
                            pong_msg = await self.create_websocket_message("pong")
                            await websocket.send(pong_msg)
                            messages_sent += 1
                            self.log("🏓 Sent pong response")
                        elif msg_type == "pong":
                            self.log("🏓 Received pong from server")

                except asyncio.TimeoutError:
                    self.log("⚠️ No heartbeat response within timeout")

                return WebSocketTestReport(
                    test_name="Heartbeat Mechanism",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_sent=messages_sent,
                    messages_received=len(messages_received),
                    error_message=None,
                    received_messages=messages_received,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return WebSocketTestReport(
                test_name="Heartbeat Mechanism",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_message_broadcasting(
        self, websocket_url: str
    ) -> WebSocketTestReport:
        """Test message broadcasting and reception"""
        start_time = time.time()
        messages_sent = 0
        messages_received = []

        try:
            self.log("Testing message broadcasting")

            async with websockets.connect(websocket_url) as websocket:
                # Wait for welcome message
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                messages_received.append(json.loads(welcome_msg))

                # Subscribe to all message types
                sub_msg = await self.create_subscription_message("subscribe", "all")
                await websocket.send(sub_msg)
                messages_sent += 1
                self.log("📡 Subscribed to all message types")

                # Listen for broadcast messages for a period
                self.log("👂 Listening for broadcast messages...")
                listen_duration = 10.0  # Listen for 10 seconds
                end_time = time.time() + listen_duration

                while time.time() < end_time:
                    try:
                        message = await asyncio.wait_for(
                            websocket.recv(), timeout=min(2.0, end_time - time.time())
                        )
                        message_data = json.loads(message)
                        messages_received.append(message_data)

                        msg_type = message_data.get("type")
                        self.log(f"📨 Received broadcast: {msg_type}")

                        if self.verbose:
                            self.log(
                                f"Message data: {json.dumps(message_data, indent=2)[:200]}..."
                            )

                    except asyncio.TimeoutError:
                        continue  # Continue listening

                self.log(f"📊 Received {len(messages_received) - 1} broadcast messages")

                return WebSocketTestReport(
                    test_name="Message Broadcasting",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_sent=messages_sent,
                    messages_received=len(messages_received),
                    error_message=None,
                    received_messages=messages_received,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return WebSocketTestReport(
                test_name="Message Broadcasting",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_connection_resilience(
        self, websocket_url: str
    ) -> WebSocketTestReport:
        """Test connection resilience and error handling"""
        start_time = time.time()
        messages_sent = 0
        messages_received = []

        try:
            self.log("Testing connection resilience")

            # Test multiple rapid connections
            for i in range(3):
                try:
                    async with websockets.connect(websocket_url) as websocket:
                        self.log(f"✅ Rapid connection {i + 1} established")

                        # Send a quick message
                        test_msg = await self.create_websocket_message("heartbeat")
                        await websocket.send(test_msg)
                        messages_sent += 1

                        # Try to receive response
                        try:
                            response = await asyncio.wait_for(
                                websocket.recv(), timeout=2.0
                            )
                            messages_received.append(json.loads(response))
                        except asyncio.TimeoutError:
                            pass

                        await websocket.close()
                        self.log(f"✅ Rapid connection {i + 1} closed")

                except Exception as e:
                    self.log(f"⚠️ Rapid connection {i + 1} failed: {e}")

                await asyncio.sleep(0.5)  # Brief pause between connections

            return WebSocketTestReport(
                test_name="Connection Resilience",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=None,
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

        except Exception as e:
            return WebSocketTestReport(
                test_name="Connection Resilience",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_invalid_messages(self, websocket_url: str) -> WebSocketTestReport:
        """Test handling of invalid messages"""
        start_time = time.time()
        messages_sent = 0
        messages_received = []

        try:
            self.log("Testing invalid message handling")

            async with websockets.connect(websocket_url) as websocket:
                # Wait for welcome message
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                messages_received.append(json.loads(welcome_msg))

                # Test invalid JSON
                invalid_messages = [
                    "invalid json",
                    '{"type": "invalid_type"}',
                    '{"missing": "type_field"}',
                    '{"type": "", "data": null}',
                    '{"type": "subscribe", "data": {"invalid": "subscription"}}',
                ]

                for invalid_msg in invalid_messages:
                    try:
                        await websocket.send(invalid_msg)
                        messages_sent += 1
                        self.log(f"📤 Sent invalid message: {invalid_msg[:50]}...")

                        # Check for error response
                        try:
                            response = await asyncio.wait_for(
                                websocket.recv(), timeout=2.0
                            )
                            response_data = json.loads(response)
                            messages_received.append(response_data)

                            if response_data.get("type") == "error":
                                self.log(
                                    "✅ Received error response for invalid message"
                                )

                        except asyncio.TimeoutError:
                            pass  # No response is also acceptable

                    except Exception as e:
                        self.log(f"⚠️ Error sending invalid message: {e}")

                return WebSocketTestReport(
                    test_name="Invalid Message Handling",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_sent=messages_sent,
                    messages_received=len(messages_received),
                    error_message=None,
                    received_messages=messages_received,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return WebSocketTestReport(
                test_name="Invalid Message Handling",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=messages_sent,
                messages_received=len(messages_received),
                error_message=str(e),
                received_messages=messages_received,
                timestamp=datetime.now(),
            )

    async def test_concurrent_connections(
        self, websocket_url: str, num_connections: int = 5
    ) -> WebSocketTestReport:
        """Test multiple concurrent WebSocket connections"""
        start_time = time.time()
        total_messages_sent = 0
        total_messages_received = 0
        all_received_messages = []

        try:
            self.log(f"Testing {num_connections} concurrent connections")

            async def single_connection_test(connection_id: int):
                messages_sent = 0
                messages_received = []

                try:
                    async with websockets.connect(websocket_url) as websocket:
                        self.log(
                            f"✅ Concurrent connection {connection_id} established"
                        )

                        # Wait for welcome message
                        welcome_msg = await asyncio.wait_for(
                            websocket.recv(), timeout=5.0
                        )
                        messages_received.append(json.loads(welcome_msg))

                        # Send subscription message
                        sub_msg = await self.create_subscription_message(
                            "subscribe", "status"
                        )
                        await websocket.send(sub_msg)
                        messages_sent += 1

                        # Send heartbeat
                        heartbeat_msg = await self.create_websocket_message(
                            "heartbeat",
                            {"client_id": f"{self.client_id}-{connection_id}"},
                        )
                        await websocket.send(heartbeat_msg)
                        messages_sent += 1

                        # Listen for a short period
                        listen_time = 3.0
                        end_time = time.time() + listen_time

                        while time.time() < end_time:
                            try:
                                message = await asyncio.wait_for(
                                    websocket.recv(),
                                    timeout=min(1.0, end_time - time.time()),
                                )
                                messages_received.append(json.loads(message))
                            except asyncio.TimeoutError:
                                break

                        self.log(f"✅ Concurrent connection {connection_id} completed")
                        return messages_sent, messages_received

                except Exception as e:
                    self.log(f"❌ Concurrent connection {connection_id} failed: {e}")
                    return messages_sent, messages_received

            # Run all connections concurrently
            tasks = [single_connection_test(i) for i in range(num_connections)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Aggregate results
            for result in results:
                if isinstance(result, tuple):
                    sent, received = result
                    total_messages_sent += sent
                    total_messages_received += len(received)
                    all_received_messages.extend(received)

            success_rate = (
                len([r for r in results if isinstance(r, tuple)])
                / num_connections
                * 100
            )

            self.log(f"📊 Concurrent test completed: {success_rate:.1f}% success rate")
            self.log(
                f"📊 Total messages: {total_messages_sent} sent, {total_messages_received} received"
            )

            result_status = TestResult.PASS if success_rate >= 80 else TestResult.WARN

            return WebSocketTestReport(
                test_name=f"Concurrent Connections ({num_connections})",
                result=result_status,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=total_messages_sent,
                messages_received=total_messages_received,
                error_message=f"Success rate: {success_rate:.1f}%"
                if success_rate < 100
                else None,
                received_messages=all_received_messages,
                timestamp=datetime.now(),
            )

        except Exception as e:
            return WebSocketTestReport(
                test_name=f"Concurrent Connections ({num_connections})",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_sent=total_messages_sent,
                messages_received=total_messages_received,
                error_message=str(e),
                received_messages=all_received_messages,
                timestamp=datetime.now(),
            )

    async def run_single_test(
        self, test_func: Callable, websocket_url: str, *args
    ) -> WebSocketTestReport:
        """Run a single WebSocket test"""
        try:
            return await test_func(websocket_url, *args)
        except Exception as e:
            return WebSocketTestReport(
                test_name=test_func.__name__,
                result=TestResult.FAIL,
                duration_ms=0,
                messages_sent=0,
                messages_received=0,
                error_message=str(e),
                received_messages=[],
                timestamp=datetime.now(),
            )

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all WebSocket tests"""
        self.log("🚀 Starting Wellbot Bridge WebSocket Connection Tests")
        self.log(f"Chat-port URL: {self.chat_port_url}")
        self.log(f"Bridge URL: {self.bridge_url}")
        self.log(f"Timeout: {self.timeout}s")

        start_time = time.time()

        # Define test cases
        test_cases = [
            (
                "Chat-port Basic Connection",
                self.test_basic_connection,
                self.chat_port_url,
            ),
            (
                "Chat-port Subscription",
                self.test_subscription_mechanism,
                self.chat_port_url,
            ),
            ("Chat-port Heartbeat", self.test_heartbeat_mechanism, self.chat_port_url),
            (
                "Chat-port Broadcasting",
                self.test_message_broadcasting,
                self.chat_port_url,
            ),
            (
                "Chat-port Resilience",
                self.test_connection_resilience,
                self.chat_port_url,
            ),
            (
                "Chat-port Invalid Messages",
                self.test_invalid_messages,
                self.chat_port_url,
            ),
            (
                "Chat-port Concurrent (3)",
                self.test_concurrent_connections,
                self.chat_port_url,
                3,
            ),
        ]

        # Run tests
        for test_name, test_func, *args in test_cases:
            self.log(f"Running: {test_name}")

            report = await self.run_single_test(test_func, *args)
            report.test_name = test_name
            self.test_reports.append(report)

            # Log result
            if report.result == TestResult.PASS:
                self.log(f"✅ {test_name} - {report.duration_ms:.1f}ms", "PASS")
            elif report.result == TestResult.WARN:
                self.log(f"⚠️ {test_name} - {report.error_message}", "WARN")
            else:
                self.log(f"❌ {test_name} - {report.error_message}", "ERROR")

            if self.verbose and report.received_messages:
                self.log(
                    f"Messages: {report.messages_sent} sent, {report.messages_received} received"
                )

            await asyncio.sleep(1)  # Brief pause between tests

        total_time = time.time() - start_time
        self.log(f"🏁 All WebSocket tests completed in {total_time:.1f}s")

        return self.generate_summary()

    def generate_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        if not self.test_reports:
            return {"error": "No tests executed"}

        total_tests = len(self.test_reports)
        passed_tests = sum(1 for r in self.test_reports if r.result == TestResult.PASS)
        failed_tests = sum(1 for r in self.test_reports if r.result == TestResult.FAIL)
        warned_tests = sum(1 for r in self.test_reports if r.result == TestResult.WARN)

        total_messages_sent = sum(r.messages_sent for r in self.test_reports)
        total_messages_received = sum(r.messages_received for r in self.test_reports)

        avg_duration = (
            sum(r.duration_ms for r in self.test_reports) / total_tests
            if total_tests > 0
            else 0
        )

        summary = {
            "test_execution": {
                "timestamp": datetime.now().isoformat(),
                "chat_port_url": self.chat_port_url,
                "bridge_url": self.bridge_url,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "warned_tests": warned_tests,
                "success_rate": (passed_tests / total_tests) * 100
                if total_tests > 0
                else 0,
            },
            "communication": {
                "total_messages_sent": total_messages_sent,
                "total_messages_received": total_messages_received,
                "avg_test_duration_ms": avg_duration,
            },
            "test_details": [
                {
                    "name": r.test_name,
                    "result": r.result.value,
                    "duration_ms": r.duration_ms,
                    "messages_sent": r.messages_sent,
                    "messages_received": r.messages_received,
                    "error_message": r.error_message,
                }
                for r in self.test_reports
            ],
        }

        # Print summary
        self.log("📋 WebSocket Test Summary:")
        self.log(
            f"   Total: {total_tests}, Passed: {passed_tests}, Failed: {failed_tests}, Warned: {warned_tests}"
        )
        self.log(f"   Success Rate: {summary['test_execution']['success_rate']:.1f}%")
        self.log(
            f"   Messages: {total_messages_sent} sent, {total_messages_received} received"
        )
        self.log(f"   Avg Duration: {avg_duration:.1f}ms")

        return summary

    def save_report(self, filename: Optional[str] = None) -> str:
        """Save detailed test report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"websocket_test_report_{timestamp}.json"

        summary = self.generate_summary()

        with open(filename, "w") as f:
            json.dump(summary, f, indent=2, default=str)

        self.log(f"📄 Report saved to: {filename}")
        return filename


async def main():
    """Main test execution function"""
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge WebSocket Connection Tester"
    )
    parser.add_argument(
        "--chat-port-url",
        default="ws://localhost:8081/ws",
        help="Chat-port WebSocket URL (default: ws://localhost:8081/ws)",
    )
    parser.add_argument(
        "--bridge-url",
        default="ws://localhost:3030/ws",
        help="Bridge WebSocket URL (default: ws://localhost:3030/ws)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=30.0,
        help="Connection timeout in seconds (default: 30.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=3,
        help="Number of concurrent connections to test (default: 3)",
    )

    args = parser.parse_args()

    # Initialize tester
    tester = WebSocketTester(
        chat_port_url=args.chat_port_url,
        bridge_url=args.bridge_url,
        timeout=args.timeout,
        verbose=args.verbose,
    )

    try:
        # Run WebSocket tests
        summary = await tester.run_all_tests()

        # Save report if requested
        if args.save_report:
            tester.save_report()

        # Exit with appropriate code
        failed_tests = summary["test_execution"]["failed_tests"]
        sys.exit(1 if failed_tests > 0 else 0)

    except KeyboardInterrupt:
        tester.log("🛑 Tests interrupted by user", "WARN")
        sys.exit(130)
    except Exception as e:
        tester.log(f"💥 Unexpected error: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
