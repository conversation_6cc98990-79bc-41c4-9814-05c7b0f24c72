#!/bin/bash
# health_check_test.sh - Comprehensive automated health endpoint testing
# Usage: ./health_check_test.sh [base_url] [timeout]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8081}"
TIMEOUT="${2:-10}"
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
REPORT_FILE="health_test_report_${TIMESTAMP}.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test endpoints
declare -A ENDPOINTS=(
    ["/health"]="Main health check endpoint"
    ["/health/ready"]="Kubernetes readiness check"
    ["/health/live"]="Kubernetes liveness check"
    ["/health/trends"]="Health trends analysis"
    ["/health/metrics"]="Performance metrics"
    ["/health/circuit-breakers"]="Circuit breaker status"
    ["/health/recovery"]="Manual recovery trigger"
    ["/health/history"]="Health check history"
    ["/health/alerts"]="Active alerts"
    ["/"]="API documentation"
)

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$REPORT_FILE"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1" | tee -a "$REPORT_FILE"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1" | tee -a "$REPORT_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$REPORT_FILE"
}

test_endpoint() {
    local endpoint="$1"
    local description="$2"
    local expected_status="${3:-200}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    log_info "Testing $endpoint - $description"
    
    # Make request and capture response
    local response
    response=$(curl -s -w "%{http_code}:%{time_total}:%{size_download}" \
                   --max-time "$TIMEOUT" \
                   "$BASE_URL$endpoint" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_error "$endpoint - Request failed (timeout or connection error)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # Parse response
    local http_code time_total size_download
    IFS=':' read -r http_code time_total size_download <<< "$response"
    
    # Validate HTTP status code
    if [ "$http_code" -eq "$expected_status" ]; then
        log_success "$endpoint - HTTP $http_code - ${time_total}s - ${size_download} bytes"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # Additional validations for specific endpoints
        case "$endpoint" in
            "/health")
                validate_health_response "$BASE_URL$endpoint"
                ;;
            "/health/metrics")
                validate_metrics_response "$BASE_URL$endpoint"
                ;;
            "/health/trends")
                validate_trends_response "$BASE_URL$endpoint"
                ;;
        esac
        
        return 0
    else
        log_error "$endpoint - Expected HTTP $expected_status, got $http_code - ${time_total}s"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

validate_health_response() {
    local url="$1"
    local response
    response=$(curl -s "$url" 2>/dev/null)
    
    # Check for required fields in health response
    if echo "$response" | jq -e '.status' >/dev/null 2>&1; then
        log_success "Health response contains status field"
    else
        log_warning "Health response missing status field"
    fi
    
    if echo "$response" | jq -e '.timestamp' >/dev/null 2>&1; then
        log_success "Health response contains timestamp field"
    else
        log_warning "Health response missing timestamp field"
    fi
    
    if echo "$response" | jq -e '.components' >/dev/null 2>&1; then
        log_success "Health response contains components field"
    else
        log_warning "Health response missing components field"
    fi
}

validate_metrics_response() {
    local url="$1"
    local response
    response=$(curl -s "$url" 2>/dev/null)
    
    # Check for metrics fields
    if echo "$response" | jq -e '.memory_usage_percent' >/dev/null 2>&1; then
        log_success "Metrics response contains memory usage"
    else
        log_warning "Metrics response missing memory usage"
    fi
    
    if echo "$response" | jq -e '.cpu_usage_percent' >/dev/null 2>&1; then
        log_success "Metrics response contains CPU usage"
    else
        log_warning "Metrics response missing CPU usage"
    fi
}

validate_trends_response() {
    local url="$1"
    local response
    response=$(curl -s "$url" 2>/dev/null)
    
    # Check for trends fields
    if echo "$response" | jq -e '.availability_trend' >/dev/null 2>&1; then
        log_success "Trends response contains availability trend"
    else
        log_warning "Trends response missing availability trend"
    fi
}

performance_test() {
    log_info "Running performance tests..."
    
    local endpoint="/health"
    local requests=10
    local concurrency=5
    
    log_info "Testing $endpoint with $requests requests, concurrency $concurrency"
    
    # Use Apache Bench if available
    if command -v ab >/dev/null 2>&1; then
        ab -n "$requests" -c "$concurrency" "$BASE_URL$endpoint" > "ab_results_${TIMESTAMP}.txt" 2>&1
        
        if [ $? -eq 0 ]; then
            local rps
            rps=$(grep "Requests per second" "ab_results_${TIMESTAMP}.txt" | awk '{print $4}')
            log_success "Performance test completed - $rps requests/second"
        else
            log_error "Performance test failed"
        fi
    else
        log_warning "Apache Bench (ab) not available, skipping performance test"
    fi
}

connectivity_test() {
    log_info "Testing connectivity to $BASE_URL..."
    
    if curl -s --max-time 5 "$BASE_URL" >/dev/null 2>&1; then
        log_success "Service is reachable"
        return 0
    else
        log_error "Service is not reachable at $BASE_URL"
        return 1
    fi
}

# Main execution
main() {
    echo "=====================================" | tee "$REPORT_FILE"
    echo "Wellbot Bridge Health Check Test Suite" | tee -a "$REPORT_FILE"
    echo "=====================================" | tee -a "$REPORT_FILE"
    echo "Timestamp: $(date)" | tee -a "$REPORT_FILE"
    echo "Base URL: $BASE_URL" | tee -a "$REPORT_FILE"
    echo "Timeout: ${TIMEOUT}s" | tee -a "$REPORT_FILE"
    echo "=====================================" | tee -a "$REPORT_FILE"
    echo "" | tee -a "$REPORT_FILE"
    
    # Test connectivity first
    if ! connectivity_test; then
        log_error "Cannot reach service, aborting tests"
        exit 1
    fi
    
    echo "" | tee -a "$REPORT_FILE"
    log_info "Starting endpoint tests..."
    echo "" | tee -a "$REPORT_FILE"
    
    # Test all endpoints
    for endpoint in "${!ENDPOINTS[@]}"; do
        test_endpoint "$endpoint" "${ENDPOINTS[$endpoint]}"
        echo "" | tee -a "$REPORT_FILE"
    done
    
    # Run performance tests
    echo "" | tee -a "$REPORT_FILE"
    performance_test
    
    # Generate summary
    echo "" | tee -a "$REPORT_FILE"
    echo "=====================================" | tee -a "$REPORT_FILE"
    echo "Test Summary" | tee -a "$REPORT_FILE"
    echo "=====================================" | tee -a "$REPORT_FILE"
    echo "Total Tests: $TOTAL_TESTS" | tee -a "$REPORT_FILE"
    echo "Passed: $PASSED_TESTS" | tee -a "$REPORT_FILE"
    echo "Failed: $FAILED_TESTS" | tee -a "$REPORT_FILE"
    
    local success_rate
    if [ "$TOTAL_TESTS" -gt 0 ]; then
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        echo "Success Rate: ${success_rate}%" | tee -a "$REPORT_FILE"
    fi
    
    echo "Report saved to: $REPORT_FILE" | tee -a "$REPORT_FILE"
    
    # Exit with appropriate code
    if [ "$FAILED_TESTS" -eq 0 ]; then
        log_success "All tests passed!"
        exit 0
    else
        log_error "$FAILED_TESTS test(s) failed"
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        log_warning "jq not found - JSON validation will be limited"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        exit 1
    fi
}

# Script entry point
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [base_url] [timeout]"
    echo ""
    echo "Arguments:"
    echo "  base_url  Base URL of the service (default: http://localhost:8081)"
    echo "  timeout   Request timeout in seconds (default: 10)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Test localhost with defaults"
    echo "  $0 http://localhost:8081 15          # Custom URL and timeout"
    echo "  $0 https://api.example.com 30        # Production testing"
    exit 0
fi

check_dependencies
main
