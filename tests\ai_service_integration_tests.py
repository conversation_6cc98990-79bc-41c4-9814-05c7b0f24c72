#!/usr/bin/env python3
"""
Comprehensive AI Service Integration Tests for Wellbot Bridge Service

This module provides manual testing capabilities for AI service integration,
including API communication, request/response handling, error scenarios,
timeout testing, and performance validation using Python requests library.

Usage:
    python ai_service_integration_tests.py
    python ai_service_integration_tests.py --ai-url http://localhost:8000
    python ai_service_integration_tests.py --verbose --timeout 60
"""

import argparse
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import requests
from requests.exceptions import Timeout
from dataclasses import dataclass
from enum import Enum


class TestResult(Enum):
    """Test result status"""

    PASS = "PASS"
    FAIL = "FAIL"
    WARN = "WARN"
    SKIP = "SKIP"


@dataclass
class AITestCase:
    """AI service test case definition"""

    name: str
    description: str
    test_func: Callable
    timeout: float = 60.0
    requires_ai: bool = True
    setup_func: Optional[Callable] = None
    cleanup_func: Optional[Callable] = None


@dataclass
class AITestReport:
    """AI service test execution report"""

    test_name: str
    result: TestResult
    duration_ms: float
    ai_requests_made: int
    tokens_used: int
    processing_time_ms: float
    error_message: Optional[str]
    response_data: Optional[Dict]
    timestamp: datetime


class AIServiceIntegrationTester:
    """Comprehensive AI service integration testing suite"""

    def __init__(
        self,
        ai_service_url: str = "http://localhost:8000",
        bridge_url: str = "http://localhost:3030",
        timeout: float = 60.0,
        verbose: bool = False,
    ):
        self.ai_service_url = ai_service_url.rstrip("/")
        self.bridge_url = bridge_url.rstrip("/")
        self.timeout = timeout
        self.verbose = verbose
        self.test_reports: List[AITestReport] = []
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Wellbot-AI-Tester/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-API-Key": "wellbot-dev-key-2025",  # Default API key
            }
        )

    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp and color coding"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if level == "ERROR":
            print(f"\033[91m[{timestamp}] [FAIL] {message}\033[0m")
        elif level == "WARN":
            print(f"\033[93m[{timestamp}] [WARN] {message}\033[0m")
        elif level == "PASS":
            print(f"\033[92m[{timestamp}] [PASS] {message}\033[0m")
        elif level == "INFO":
            print(f"\033[94m[{timestamp}] [INFO] {message}\033[0m")
        else:
            print(f"[{timestamp}] [{level}] {message}")

    def create_ai_request(
        self,
        prompt: str,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Create an AI explanation request"""
        request: Dict[str, Any] = {"prompt": prompt}
        if temperature is not None:
            request["temperature"] = temperature
        if max_tokens is not None:
            request["max_tokens"] = max_tokens
        return request

    def test_ai_service_health(self) -> AITestReport:
        """Test AI service health endpoint"""
        start_time = time.time()
        ai_requests = 0

        try:
            self.log("Testing AI service health endpoint")

            # Test health endpoint
            health_response = self.session.get(
                f"{self.ai_service_url}/api/v1/health", timeout=self.timeout
            )
            ai_requests += 1

            if health_response.status_code == 200:
                health_data = health_response.json()
                self.log("✅ AI service health check passed")
                if self.verbose:
                    self.log(f"Status: {health_data.get('status')}")
                    self.log(f"Model: {health_data.get('model')}")
                    self.log(f"Version: {health_data.get('version')}")

                return AITestReport(
                    test_name="AI Service Health",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    ai_requests_made=ai_requests,
                    tokens_used=0,
                    processing_time_ms=0,
                    error_message=None,
                    response_data=health_data,
                    timestamp=datetime.now(),
                )
            else:
                return AITestReport(
                    test_name="AI Service Health",
                    result=TestResult.FAIL,
                    duration_ms=(time.time() - start_time) * 1000,
                    ai_requests_made=ai_requests,
                    tokens_used=0,
                    processing_time_ms=0,
                    error_message=f"Health check failed with status {health_response.status_code}",
                    response_data=None,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return AITestReport(
                test_name="AI Service Health",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_basic_ai_explanation(self) -> AITestReport:
        """Test basic AI explanation generation"""
        start_time = time.time()
        ai_requests = 0
        tokens_used = 0
        processing_time = 0

        try:
            self.log("Testing basic AI explanation generation")

            # Create a simple explanation request
            ai_request = self.create_ai_request(
                "Explain what is artificial intelligence in simple terms",
                temperature=0.7,
                max_tokens=200,
            )

            # Send explanation request
            explanation_response = self.session.post(
                f"{self.ai_service_url}/api/v1/explanations",
                json=ai_request,
                timeout=self.timeout,
            )
            ai_requests += 1

            if explanation_response.status_code == 200:
                explanation_data = explanation_response.json()
                tokens_used = explanation_data.get("tokens_used", 0)
                processing_time = explanation_data.get("processing_time_ms", 0)

                self.log("✅ AI explanation generated successfully")
                if self.verbose:
                    self.log(f"Model: {explanation_data.get('model')}")
                    self.log(f"Tokens used: {tokens_used}")
                    self.log(f"Processing time: {processing_time:.2f}ms")
                    self.log(
                        f"Response: {explanation_data.get('content', '')[:100]}..."
                    )

                return AITestReport(
                    test_name="Basic AI Explanation",
                    result=TestResult.PASS,
                    duration_ms=(time.time() - start_time) * 1000,
                    ai_requests_made=ai_requests,
                    tokens_used=tokens_used,
                    processing_time_ms=processing_time,
                    error_message=None,
                    response_data=explanation_data,
                    timestamp=datetime.now(),
                )
            else:
                error_text = explanation_response.text
                return AITestReport(
                    test_name="Basic AI Explanation",
                    result=TestResult.FAIL,
                    duration_ms=(time.time() - start_time) * 1000,
                    ai_requests_made=ai_requests,
                    tokens_used=0,
                    processing_time_ms=0,
                    error_message=f"Request failed with status {explanation_response.status_code}: {error_text}",
                    response_data=None,
                    timestamp=datetime.now(),
                )

        except Exception as e:
            return AITestReport(
                test_name="Basic AI Explanation",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_ai_parameter_variations(self) -> AITestReport:
        """Test AI service with different parameter configurations"""
        start_time = time.time()
        ai_requests = 0
        total_tokens = 0
        total_processing_time = 0

        try:
            self.log("Testing AI parameter variations")

            # Test different parameter combinations
            test_scenarios = [
                {
                    "temperature": 0.1,
                    "max_tokens": 50,
                    "prompt": "Define machine learning briefly",
                },
                {
                    "temperature": 0.5,
                    "max_tokens": 100,
                    "prompt": "Explain neural networks",
                },
                {
                    "temperature": 0.9,
                    "max_tokens": 150,
                    "prompt": "Describe deep learning creatively",
                },
                {
                    "temperature": 0.7,
                    "max_tokens": 200,
                    "prompt": "What is the future of AI?",
                },
            ]

            successful_requests = 0

            for i, scenario in enumerate(test_scenarios):
                try:
                    ai_request = self.create_ai_request(**scenario)

                    response = self.session.post(
                        f"{self.ai_service_url}/api/v1/explanations",
                        json=ai_request,
                        timeout=self.timeout,
                    )
                    ai_requests += 1

                    if response.status_code == 200:
                        data = response.json()
                        tokens_used = data.get("tokens_used", 0)
                        processing_time = data.get("processing_time_ms", 0)

                        total_tokens += tokens_used
                        total_processing_time += processing_time
                        successful_requests += 1

                        self.log(
                            f"✅ Scenario {i + 1}: T={scenario['temperature']}, Tokens={tokens_used}"
                        )

                    else:
                        self.log(
                            f"⚠️ Scenario {i + 1} failed with status {response.status_code}"
                        )

                except Exception as e:
                    self.log(f"⚠️ Scenario {i + 1} error: {e}")

                # Brief pause between requests
                time.sleep(0.5)

            success_rate = (successful_requests / len(test_scenarios)) * 100
            result_status = TestResult.PASS if success_rate >= 75 else TestResult.WARN

            self.log(
                f"📊 Parameter variation test completed: {success_rate:.1f}% success rate"
            )

            return AITestReport(
                test_name="AI Parameter Variations",
                result=result_status,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=total_tokens,
                processing_time_ms=total_processing_time,
                error_message=f"Success rate: {success_rate:.1f}%"
                if success_rate < 100
                else None,
                response_data={
                    "scenarios_tested": len(test_scenarios),
                    "success_rate": success_rate,
                },
                timestamp=datetime.now(),
            )

        except Exception as e:
            return AITestReport(
                test_name="AI Parameter Variations",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_ai_error_handling(self) -> AITestReport:
        """Test AI service error handling scenarios"""
        start_time = time.time()
        ai_requests = 0

        try:
            self.log("Testing AI service error handling")

            # Test various error scenarios
            error_scenarios = [
                # Invalid API key
                {"headers": {"X-API-Key": "invalid-key"}, "expected_status": 401},
                # Missing prompt
                {"json": {"temperature": 0.7}, "expected_status": 422},
                # Invalid temperature
                {
                    "json": {"prompt": "test", "temperature": 2.0},
                    "expected_status": 422,
                },
                # Invalid max_tokens
                {"json": {"prompt": "test", "max_tokens": -1}, "expected_status": 422},
                # Empty prompt
                {"json": {"prompt": ""}, "expected_status": 422},
            ]

            successful_error_tests = 0

            for i, scenario in enumerate(error_scenarios):
                try:
                    # Prepare request
                    headers = scenario.get("headers", {})
                    json_data = scenario.get("json", {"prompt": "test"})
                    expected_status = scenario["expected_status"]

                    # Override session headers if needed
                    request_headers = dict(self.session.headers)
                    request_headers.update(headers)

                    response = requests.post(
                        f"{self.ai_service_url}/api/v1/explanations",
                        json=json_data,
                        headers=request_headers,
                        timeout=self.timeout,
                    )
                    ai_requests += 1

                    if response.status_code == expected_status:
                        successful_error_tests += 1
                        self.log(
                            f"✅ Error scenario {i + 1}: Expected {expected_status}, got {response.status_code}"
                        )
                    else:
                        self.log(
                            f"⚠️ Error scenario {i + 1}: Expected {expected_status}, got {response.status_code}"
                        )

                except Exception as e:
                    self.log(f"⚠️ Error scenario {i + 1} exception: {e}")

                # Brief pause between requests
                time.sleep(0.3)

            success_rate = (successful_error_tests / len(error_scenarios)) * 100
            result_status = TestResult.PASS if success_rate >= 80 else TestResult.WARN

            self.log(
                f"📊 Error handling test completed: {success_rate:.1f}% success rate"
            )

            return AITestReport(
                test_name="AI Error Handling",
                result=result_status,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=f"Success rate: {success_rate:.1f}%"
                if success_rate < 100
                else None,
                response_data={
                    "error_scenarios_tested": len(error_scenarios),
                    "success_rate": success_rate,
                },
                timestamp=datetime.now(),
            )

        except Exception as e:
            return AITestReport(
                test_name="AI Error Handling",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_ai_timeout_scenarios(self) -> AITestReport:
        """Test AI service timeout handling"""
        start_time = time.time()
        ai_requests = 0

        try:
            self.log("Testing AI service timeout scenarios")

            # Test with very short timeout
            short_timeout = 0.1  # 100ms - very short

            ai_request = self.create_ai_request(
                "Write a very detailed explanation of quantum computing, including its history, principles, applications, and future prospects. Please be comprehensive and thorough.",
                temperature=0.7,
                max_tokens=2000,
            )

            try:
                response = self.session.post(
                    f"{self.ai_service_url}/api/v1/explanations",
                    json=ai_request,
                    timeout=short_timeout,
                )
                ai_requests += 1

                # If we get here, the request was faster than expected
                self.log("⚠️ Request completed faster than expected timeout")

            except Timeout:
                self.log("✅ Timeout handled correctly")

                # Now test with reasonable timeout
                reasonable_timeout = 30.0

                try:
                    response = self.session.post(
                        f"{self.ai_service_url}/api/v1/explanations",
                        json=ai_request,
                        timeout=reasonable_timeout,
                    )
                    ai_requests += 1

                    if response.status_code == 200:
                        data = response.json()
                        self.log("✅ Request succeeded with reasonable timeout")

                        return AITestReport(
                            test_name="AI Timeout Scenarios",
                            result=TestResult.PASS,
                            duration_ms=(time.time() - start_time) * 1000,
                            ai_requests_made=ai_requests,
                            tokens_used=data.get("tokens_used", 0),
                            processing_time_ms=data.get("processing_time_ms", 0),
                            error_message=None,
                            response_data={"timeout_test": "passed"},
                            timestamp=datetime.now(),
                        )

                except Exception as e:
                    self.log(f"⚠️ Request failed with reasonable timeout: {e}")

            return AITestReport(
                test_name="AI Timeout Scenarios",
                result=TestResult.WARN,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message="Timeout behavior not as expected",
                response_data=None,
                timestamp=datetime.now(),
            )

        except Exception as e:
            return AITestReport(
                test_name="AI Timeout Scenarios",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_ai_performance_benchmarks(self) -> AITestReport:
        """Test AI service performance benchmarks"""
        start_time = time.time()
        ai_requests = 0
        total_tokens = 0
        total_processing_time = 0
        response_times = []

        try:
            self.log("Testing AI service performance benchmarks")

            # Performance test scenarios
            performance_tests = [
                {"prompt": "Hello", "max_tokens": 10, "name": "short_response"},
                {
                    "prompt": "Explain machine learning",
                    "max_tokens": 100,
                    "name": "medium_response",
                },
                {
                    "prompt": "Write a comprehensive guide to artificial intelligence",
                    "max_tokens": 500,
                    "name": "long_response",
                },
            ]

            benchmark_results = {}

            for test in performance_tests:
                test_start = time.time()

                ai_request = self.create_ai_request(
                    test["prompt"], temperature=0.7, max_tokens=test["max_tokens"]
                )

                try:
                    response = self.session.post(
                        f"{self.ai_service_url}/api/v1/explanations",
                        json=ai_request,
                        timeout=self.timeout,
                    )
                    ai_requests += 1

                    request_time = (time.time() - test_start) * 1000
                    response_times.append(request_time)

                    if response.status_code == 200:
                        data = response.json()
                        tokens_used = data.get("tokens_used", 0)
                        processing_time = data.get("processing_time_ms", 0)

                        total_tokens += tokens_used
                        total_processing_time += processing_time

                        benchmark_results[test["name"]] = {
                            "request_time_ms": request_time,
                            "processing_time_ms": processing_time,
                            "tokens_used": tokens_used,
                            "tokens_per_second": tokens_used / (processing_time / 1000)
                            if processing_time > 0
                            else 0,
                        }

                        self.log(
                            f"✅ {test['name']}: {request_time:.1f}ms, {tokens_used} tokens"
                        )

                    else:
                        self.log(
                            f"⚠️ {test['name']} failed with status {response.status_code}"
                        )

                except Exception as e:
                    self.log(f"⚠️ {test['name']} error: {e}")

                # Brief pause between performance tests
                time.sleep(1)

            # Calculate performance metrics
            avg_response_time = (
                sum(response_times) / len(response_times) if response_times else 0
            )
            avg_processing_time = (
                total_processing_time / ai_requests if ai_requests > 0 else 0
            )
            avg_tokens_per_request = (
                total_tokens / ai_requests if ai_requests > 0 else 0
            )

            self.log("📊 Performance summary:")
            self.log(f"   Avg response time: {avg_response_time:.1f}ms")
            self.log(f"   Avg processing time: {avg_processing_time:.1f}ms")
            self.log(f"   Avg tokens per request: {avg_tokens_per_request:.1f}")

            # Determine result based on performance
            result_status = TestResult.PASS
            if avg_response_time > 10000:  # 10 seconds
                result_status = TestResult.WARN

            return AITestReport(
                test_name="AI Performance Benchmarks",
                result=result_status,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=total_tokens,
                processing_time_ms=total_processing_time,
                error_message=f"Avg response time: {avg_response_time:.1f}ms"
                if avg_response_time > 5000
                else None,
                response_data={
                    "benchmark_results": benchmark_results,
                    "avg_response_time_ms": avg_response_time,
                    "avg_processing_time_ms": avg_processing_time,
                    "avg_tokens_per_request": avg_tokens_per_request,
                },
                timestamp=datetime.now(),
            )

        except Exception as e:
            return AITestReport(
                test_name="AI Performance Benchmarks",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def test_bridge_ai_integration(self) -> AITestReport:
        """Test AI integration through the bridge service"""
        start_time = time.time()
        ai_requests = 0

        try:
            self.log("Testing AI integration through bridge service")

            # Check if bridge service is available
            try:
                bridge_health = self.session.get(f"{self.bridge_url}/health", timeout=5)
                if bridge_health.status_code != 200:
                    return AITestReport(
                        test_name="Bridge AI Integration",
                        result=TestResult.SKIP,
                        duration_ms=(time.time() - start_time) * 1000,
                        ai_requests_made=0,
                        tokens_used=0,
                        processing_time_ms=0,
                        error_message="Bridge service not available",
                        response_data=None,
                        timestamp=datetime.now(),
                    )
            except Exception:
                return AITestReport(
                    test_name="Bridge AI Integration",
                    result=TestResult.SKIP,
                    duration_ms=(time.time() - start_time) * 1000,
                    ai_requests_made=0,
                    tokens_used=0,
                    processing_time_ms=0,
                    error_message="Bridge service unreachable",
                    response_data=None,
                    timestamp=datetime.now(),
                )

            # Test bridge AI endpoints if available
            self.log("✅ Bridge service is available")

            # This would test actual bridge AI integration endpoints
            # For now, we'll just verify the bridge can communicate with AI service

            return AITestReport(
                test_name="Bridge AI Integration",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=None,
                response_data={"bridge_status": "available"},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return AITestReport(
                test_name="Bridge AI Integration",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                ai_requests_made=ai_requests,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def run_single_test(self, test_func: Callable) -> AITestReport:
        """Run a single AI service test"""
        try:
            return test_func()
        except Exception as e:
            return AITestReport(
                test_name=test_func.__name__,
                result=TestResult.FAIL,
                duration_ms=0,
                ai_requests_made=0,
                tokens_used=0,
                processing_time_ms=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all AI service integration tests"""
        self.log("🚀 Starting Wellbot Bridge AI Service Integration Tests")
        self.log(f"AI Service URL: {self.ai_service_url}")
        self.log(f"Bridge URL: {self.bridge_url}")
        self.log(f"Timeout: {self.timeout}s")

        start_time = time.time()

        # Define test cases
        test_cases = [
            ("AI Service Health", self.test_ai_service_health),
            ("Basic AI Explanation", self.test_basic_ai_explanation),
            ("AI Parameter Variations", self.test_ai_parameter_variations),
            ("AI Error Handling", self.test_ai_error_handling),
            ("AI Timeout Scenarios", self.test_ai_timeout_scenarios),
            ("AI Performance Benchmarks", self.test_ai_performance_benchmarks),
            ("Bridge AI Integration", self.test_bridge_ai_integration),
        ]

        # Run tests
        for test_name, test_func in test_cases:
            self.log(f"Running: {test_name}")

            report = self.run_single_test(test_func)
            report.test_name = test_name
            self.test_reports.append(report)

            # Log result
            if report.result == TestResult.PASS:
                self.log(f"✅ {test_name} - {report.duration_ms:.1f}ms", "PASS")
            elif report.result == TestResult.WARN:
                self.log(f"⚠️ {test_name} - {report.error_message}", "WARN")
            elif report.result == TestResult.SKIP:
                self.log(f"⏭️ {test_name} - {report.error_message}", "INFO")
            else:
                self.log(f"❌ {test_name} - {report.error_message}", "ERROR")

            if self.verbose:
                self.log(
                    f"AI Requests: {report.ai_requests_made}, Tokens: {report.tokens_used}, Processing: {report.processing_time_ms:.1f}ms"
                )

            time.sleep(1)  # Brief pause between tests

        total_time = time.time() - start_time
        self.log(f"🏁 All AI service integration tests completed in {total_time:.1f}s")

        return self.generate_summary()

    def generate_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        if not self.test_reports:
            return {"error": "No tests executed"}

        total_tests = len(self.test_reports)
        passed_tests = sum(1 for r in self.test_reports if r.result == TestResult.PASS)
        failed_tests = sum(1 for r in self.test_reports if r.result == TestResult.FAIL)
        warned_tests = sum(1 for r in self.test_reports if r.result == TestResult.WARN)
        skipped_tests = sum(1 for r in self.test_reports if r.result == TestResult.SKIP)

        total_ai_requests = sum(r.ai_requests_made for r in self.test_reports)
        total_tokens = sum(r.tokens_used for r in self.test_reports)
        total_processing_time = sum(r.processing_time_ms for r in self.test_reports)

        avg_duration = (
            sum(r.duration_ms for r in self.test_reports) / total_tests
            if total_tests > 0
            else 0
        )
        avg_processing_time = (
            total_processing_time / total_ai_requests if total_ai_requests > 0 else 0
        )

        summary = {
            "test_execution": {
                "timestamp": datetime.now().isoformat(),
                "ai_service_url": self.ai_service_url,
                "bridge_url": self.bridge_url,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "warned_tests": warned_tests,
                "skipped_tests": skipped_tests,
                "success_rate": (passed_tests / total_tests) * 100
                if total_tests > 0
                else 0,
            },
            "ai_performance": {
                "total_ai_requests": total_ai_requests,
                "total_tokens_used": total_tokens,
                "total_processing_time_ms": total_processing_time,
                "avg_test_duration_ms": avg_duration,
                "avg_processing_time_ms": avg_processing_time,
                "avg_tokens_per_request": total_tokens / total_ai_requests
                if total_ai_requests > 0
                else 0,
            },
            "test_details": [
                {
                    "name": r.test_name,
                    "result": r.result.value,
                    "duration_ms": r.duration_ms,
                    "ai_requests_made": r.ai_requests_made,
                    "tokens_used": r.tokens_used,
                    "processing_time_ms": r.processing_time_ms,
                    "error_message": r.error_message,
                }
                for r in self.test_reports
            ],
        }

        # Print summary
        self.log("📋 AI Service Integration Test Summary:")
        self.log(
            f"   Total: {total_tests}, Passed: {passed_tests}, Failed: {failed_tests}, Warned: {warned_tests}, Skipped: {skipped_tests}"
        )
        self.log(f"   Success Rate: {summary['test_execution']['success_rate']:.1f}%")
        self.log(f"   AI Requests: {total_ai_requests}")
        self.log(f"   Tokens Used: {total_tokens}")
        self.log(f"   Avg Processing Time: {avg_processing_time:.1f}ms")
        self.log(f"   Avg Test Duration: {avg_duration:.1f}ms")

        return summary

    def save_report(self, filename: Optional[str] = None) -> str:
        """Save detailed test report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ai_service_integration_test_report_{timestamp}.json"

        summary = self.generate_summary()

        with open(filename, "w") as f:
            json.dump(summary, f, indent=2, default=str)

        self.log(f"📄 Report saved to: {filename}")
        return filename


def main():
    """Main test execution function"""
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge AI Service Integration Tester"
    )
    parser.add_argument(
        "--ai-url",
        default="http://localhost:8000",
        help="AI service URL (default: http://localhost:8000)",
    )
    parser.add_argument(
        "--bridge-url",
        default="http://localhost:3030",
        help="Bridge service URL (default: http://localhost:3030)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=60.0,
        help="Request timeout in seconds (default: 60.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )

    args = parser.parse_args()

    # Initialize tester
    tester = AIServiceIntegrationTester(
        ai_service_url=args.ai_url,
        bridge_url=args.bridge_url,
        timeout=args.timeout,
        verbose=args.verbose,
    )

    try:
        # Run AI service integration tests
        summary = tester.run_all_tests()

        # Save report if requested
        if args.save_report:
            tester.save_report()

        # Exit with appropriate code
        failed_tests = summary["test_execution"]["failed_tests"]
        sys.exit(1 if failed_tests > 0 else 0)

    except KeyboardInterrupt:
        tester.log("🛑 Tests interrupted by user", "WARN")
        sys.exit(130)
    except Exception as e:
        tester.log(f"💥 Unexpected error: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
