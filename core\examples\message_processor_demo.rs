/*!
# 🔄 Message Processor Interactive Demo

A comprehensive demonstration of the Wellbot Message Processor with JID filtering,
rate limiting, and intelligent message routing. Features a beautiful cliclack interface
for managing allowed JIDs and monitoring message processing in real-time.

## Features
- ✨ Beautiful interactive CLI with cliclack
- 🔐 JID allowlist management
- 📊 Real-time processing statistics
- 🚦 Rate limiting demonstration
- 🔄 Message deduplication
- 📈 Performance monitoring
- 🎯 Professional error handling

## Usage
```bash
cargo run --example message_processor_demo --features examples
```
*/

use cliclack::{confirm, input, intro, log, note, outro, select, spinner};
use console::style;
use std::{sync::Arc, time::Duration};
use tokio::time::sleep;
use wellbot_bridge::{
    config::Config,
    services::{
        chat_port_client::ChatPortClient,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
        message_processor::MessageProcessor,
    },
    types::IncomingWhatsAppData,
};

/// Demo operation modes
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, Eq, PartialEq)]
enum DemoMode {
    Interactive,
    Automated,
    Monitoring,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize beautiful CLI
    intro(format!(
        "{} {}",
        style("🔄 Wellbot Message Processor Demo").cyan().bold(),
        style("v1.0.0").dim()
    ))?;

    // Load configuration
    let config = load_demo_config().await?;

    // Initialize services
    let (message_processor, _chat_client) = initialize_services(config).await?;

    // Demo mode selection
    let demo_mode = select("What would you like to demonstrate?")
        .initial_value(DemoMode::Interactive)
        .item(
            DemoMode::Interactive,
            "Interactive JID Management",
            "Manage allowed JIDs and test filtering",
        )
        .item(
            DemoMode::Automated,
            "Automated Testing",
            "Run automated message processing tests",
        )
        .item(
            DemoMode::Monitoring,
            "Real-time Monitoring",
            "Monitor processing statistics in real-time",
        )
        .interact()?;

    match demo_mode {
        DemoMode::Interactive => run_interactive_demo(message_processor).await?,
        DemoMode::Automated => run_automated_demo(message_processor).await?,
        DemoMode::Monitoring => run_monitoring_demo(message_processor).await?,
    }

    outro("🎉 Message Processor Demo completed successfully!")?;
    Ok(())
}

/// Load demo configuration
async fn load_demo_config() -> Result<Config, Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("📋 Loading configuration...");

    sleep(Duration::from_millis(500)).await;

    let config = Config::from_env().unwrap_or_else(|_| {
        log::warning("Using default configuration for demo").unwrap();
        Config::default()
    });

    spinner.stop("✅ Configuration loaded successfully!");
    Ok(config)
}

/// Initialize message processor and chat client
async fn initialize_services(
    config: Config,
) -> Result<(Arc<MessageProcessor>, Arc<ChatPortClient>), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🔧 Initializing services...");

    // Initialize chat-port client
    let chat_client = Arc::new(ChatPortClient::new(config.chat_port.clone())?);

    // Initialize JID authorization service
    let jid_config = JidAuthConfig::default();
    let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);

    // Get initial allowed JIDs from user
    spinner.stop("🔐 Configure allowed JIDs");

    let default_jid = input("Enter your WhatsApp JID (phone number)")
        .placeholder("201030320366")
        .default_input("201030320366")
        .interact()?;

    // Add the default JID to the authorization service
    jid_authorization.add_jid(default_jid, None).await?;

    // Initialize message processor
    let message_processor = Arc::new(
        MessageProcessor::new(
            config.bridge.clone(),
            chat_client.clone(),
            jid_authorization,
        )
        .await,
    );

    log::success("✅ Services initialized successfully!")?;
    display_initial_stats(&message_processor).await?;

    Ok((message_processor, chat_client))
}

/// Display initial statistics
async fn display_initial_stats(
    processor: &MessageProcessor,
) -> Result<(), Box<dyn std::error::Error>> {
    let stats = processor.get_stats().await;
    let allowed_jids = processor.get_allowed_jids().await;

    let stats_info = format!(
        "📊 Initial Configuration:\n• Allowed JIDs: {} ({})\n• Max Concurrent: {}\n• Available Permits: {}\n• Cache Size: {}",
        stats.allowed_jids_count,
        allowed_jids.join(", "),
        stats.max_concurrent,
        stats.available_permits,
        stats.cached_messages
    );

    note("🎯 Message Processor Ready", stats_info)?;
    Ok(())
}

/// Run interactive demo mode
async fn run_interactive_demo(
    processor: Arc<MessageProcessor>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🎮 Starting Interactive Demo Mode")?;

    loop {
        let action = select("What would you like to do?")
            .item(
                "manage_jids",
                "🔐 Manage Allowed JIDs",
                "Add, remove, or view allowed JIDs",
            )
            .item(
                "test_message",
                "📨 Test Message Processing",
                "Send a test message",
            )
            .item(
                "view_stats",
                "📊 View Statistics",
                "Display current processing statistics",
            )
            .item(
                "simulate_load",
                "🚀 Simulate Load",
                "Test rate limiting and concurrency",
            )
            .item("exit", "🚪 Exit Demo", "Return to main menu")
            .interact()?;

        match action {
            "manage_jids" => manage_jids_interactive(&processor).await?,
            "test_message" => test_message_processing(&processor).await?,
            "view_stats" => display_detailed_stats(&processor).await?,
            "simulate_load" => simulate_load_testing(&processor).await?,
            "exit" => break,
            _ => unreachable!(),
        }
    }

    Ok(())
}

/// Manage JIDs interactively
async fn manage_jids_interactive(
    processor: &MessageProcessor,
) -> Result<(), Box<dyn std::error::Error>> {
    let current_jids = processor.get_allowed_jids().await;

    let jid_action = select("JID Management")
        .item("add", "➕ Add JID", "Add a new JID to the allowed list")
        .item(
            "remove",
            "➖ Remove JID",
            "Remove a JID from the allowed list",
        )
        .item("view", "👁️ View All", "Display all allowed JIDs")
        .interact()?;

    match jid_action {
        "add" => {
            let new_jid: String = input("Enter JID to add")
                .placeholder("201234567890")
                .interact()?;

            processor.add_allowed_jid(new_jid.clone()).await?;
            log::success(format!("✅ Added JID: {}", new_jid))?;
        }
        "remove" => {
            if current_jids.is_empty() {
                log::warning("No JIDs to remove")?;
                return Ok(());
            }

            let jid_to_remove = select("Select JID to remove")
                .items(
                    &current_jids
                        .iter()
                        .map(|jid| (jid.as_str(), jid.as_str(), ""))
                        .collect::<Vec<_>>(),
                )
                .interact()?;

            let removed = processor.remove_allowed_jid(jid_to_remove).await?;
            if removed {
                log::success(format!("✅ Removed JID: {}", jid_to_remove))?;
            } else {
                log::warning(format!("⚠️ JID not found: {}", jid_to_remove))?;
            }
        }
        "view" => {
            if current_jids.is_empty() {
                note("📋 Allowed JIDs", "No JIDs currently allowed")?;
            } else {
                let jids_list = current_jids.join("\n• ");
                note("📋 Allowed JIDs", format!("• {}", jids_list))?;
            }
        }
        _ => unreachable!(),
    }

    Ok(())
}

/// Test message processing
async fn test_message_processing(
    processor: &MessageProcessor,
) -> Result<(), Box<dyn std::error::Error>> {
    let test_jid: String = input("Enter sender JID")
        .placeholder("201030320366")
        .interact()?;

    let test_message = input("Enter test message")
        .placeholder("Hello, this is a test message!")
        .interact()?;

    let spinner = spinner();
    spinner.start("🔄 Processing test message...");

    let whatsapp_data = IncomingWhatsAppData {
        from: test_jid.clone(),
        message: test_message,
        message_id: format!("test_{}", chrono::Utc::now().timestamp()),
        timestamp: chrono::Utc::now(),
    };

    match processor.process_message(whatsapp_data).await {
        Ok(()) => {
            spinner.stop("✅ Message processed successfully!");
            log::success(format!("Message from {} was processed", test_jid))?;
        }
        Err(e) => {
            spinner.stop("❌ Message processing failed");
            log::error(format!("Processing error: {}", e))?;
        }
    }

    Ok(())
}

/// Display detailed statistics
async fn display_detailed_stats(
    processor: &MessageProcessor,
) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("📊 Gathering statistics...");

    let stats = processor.get_stats().await;

    spinner.stop("📈 Processing Statistics");

    let stats_display = format!(
        "📨 Messages:\n  • Total Received: {}\n  • Successfully Processed: {}\n  • Failed: {}\n  • Timed Out: {}\n\n🚫 Rejections:\n  • Unauthorized JID: {}\n  • Rate Limited: {}\n  • Duplicates: {}\n\n📊 Performance:\n  • Success Rate: {:.1}%\n  • Rejection Rate: {:.1}%\n  • Available Permits: {}/{}\n  • Cached Messages: {}\n  • Allowed JIDs: {}",
        stats.total_messages_received,
        stats.messages_processed_successfully,
        stats.messages_failed,
        stats.messages_timeout,
        stats.messages_rejected_jid,
        stats.messages_rejected_rate_limit,
        stats.messages_rejected_duplicate,
        stats.success_rate(),
        stats.rejection_rate(),
        stats.available_permits,
        stats.max_concurrent,
        stats.cached_messages,
        stats.allowed_jids_count
    );

    note("📈 Detailed Statistics", stats_display)?;
    Ok(())
}

/// Simulate load testing
async fn simulate_load_testing(
    processor: &MessageProcessor,
) -> Result<(), Box<dyn std::error::Error>> {
    let test_count: u32 = input("How many test messages to send?")
        .placeholder("10")
        .default_input("10")
        .interact()?;

    let use_allowed_jid = confirm("Use allowed JID for testing?")
        .initial_value(true)
        .interact()?;

    let test_jid = if use_allowed_jid {
        let allowed_jids = processor.get_allowed_jids().await;
        if allowed_jids.is_empty() {
            log::error("No allowed JIDs configured!")?;
            return Ok(());
        }
        allowed_jids[0].clone()
    } else {
        "unauthorized_jid".to_string()
    };

    let spinner = spinner();
    spinner.start(format!("🚀 Sending {} test messages...", test_count));

    let mut successful = 0;
    let mut failed = 0;

    for i in 1..=test_count {
        let whatsapp_data = IncomingWhatsAppData {
            from: test_jid.clone(),
            message: format!("Load test message #{}", i),
            message_id: format!(
                "load_test_{}_{}",
                i,
                chrono::Utc::now().timestamp_nanos_opt().unwrap()
            ),
            timestamp: chrono::Utc::now(),
        };

        match processor.process_message(whatsapp_data).await {
            Ok(()) => successful += 1,
            Err(_) => failed += 1,
        }

        // Small delay to avoid overwhelming the system
        sleep(Duration::from_millis(100)).await;
    }

    spinner.stop("🎯 Load testing completed!");

    let results = format!(
        "📊 Load Test Results:\n• Messages Sent: {}\n• Successful: {}\n• Failed: {}\n• Success Rate: {:.1}%",
        test_count,
        successful,
        failed,
        (successful as f64 / test_count as f64) * 100.0
    );

    note("🚀 Load Test Summary", results)?;

    // Show updated statistics
    display_detailed_stats(processor).await?;

    Ok(())
}

/// Run automated demo mode
async fn run_automated_demo(
    processor: Arc<MessageProcessor>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🤖 Starting Automated Demo Mode")?;

    note(
        "🎬 Automated Demo Sequence",
        "This will demonstrate:\n• JID filtering\n• Rate limiting\n• Message processing\n• Statistics tracking",
    )?;

    // Step 1: Add test JIDs
    log::info("Step 1: Adding test JIDs...")?;
    processor.add_allowed_jid("test_user_1".to_string()).await?;
    processor.add_allowed_jid("test_user_2".to_string()).await?;

    // Step 2: Test authorized messages
    log::info("Step 2: Testing authorized messages...")?;
    for i in 1..=3 {
        let whatsapp_data = IncomingWhatsAppData {
            from: "test_user_1".to_string(),
            message: format!("Authorized test message {}", i),
            message_id: format!("auth_test_{}", i),
            timestamp: chrono::Utc::now(),
        };

        let _ = processor.process_message(whatsapp_data).await;
        sleep(Duration::from_millis(500)).await;
    }

    // Step 3: Test unauthorized messages
    log::info("Step 3: Testing unauthorized messages...")?;
    for i in 1..=2 {
        let whatsapp_data = IncomingWhatsAppData {
            from: "unauthorized_user".to_string(),
            message: format!("Unauthorized test message {}", i),
            message_id: format!("unauth_test_{}", i),
            timestamp: chrono::Utc::now(),
        };

        let _ = processor.process_message(whatsapp_data).await;
        sleep(Duration::from_millis(300)).await;
    }

    // Step 4: Show final statistics
    log::info("Step 4: Displaying final statistics...")?;
    display_detailed_stats(&processor).await?;

    log::success("🎉 Automated demo completed successfully!")?;
    Ok(())
}

/// Run monitoring demo mode
async fn run_monitoring_demo(
    processor: Arc<MessageProcessor>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("📊 Starting Real-time Monitoring Mode")?;

    let duration: u64 = input("Monitoring duration (seconds)")
        .placeholder("30")
        .default_input("30")
        .interact()?;

    note(
        "📡 Real-time Monitoring",
        format!(
            "Monitoring message processor for {} seconds...\nSend WhatsApp messages to see real-time updates!",
            duration
        ),
    )?;

    let start_time = std::time::Instant::now();
    let mut last_stats = processor.get_stats().await;

    while start_time.elapsed().as_secs() < duration {
        sleep(Duration::from_secs(2)).await;

        let current_stats = processor.get_stats().await;

        // Check if stats have changed
        if current_stats.total_messages_received != last_stats.total_messages_received {
            let change_info = format!(
                "📈 Stats Update [{}s]:\n• New Messages: +{}\n• Total Processed: {}\n• Success Rate: {:.1}%\n• Rejections: {} (JID: {}, Rate: {}, Dup: {})",
                start_time.elapsed().as_secs(),
                current_stats.total_messages_received - last_stats.total_messages_received,
                current_stats.messages_processed_successfully,
                current_stats.success_rate(),
                current_stats.messages_rejected_jid
                    + current_stats.messages_rejected_rate_limit
                    + current_stats.messages_rejected_duplicate,
                current_stats.messages_rejected_jid,
                current_stats.messages_rejected_rate_limit,
                current_stats.messages_rejected_duplicate
            );

            log::info(change_info)?;
            last_stats = current_stats;
        }
    }

    log::success("📊 Monitoring session completed!")?;
    display_detailed_stats(&processor).await?;

    Ok(())
}
