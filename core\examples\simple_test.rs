/*!
# 🚀 Simple Bridge Service Test

A minimal test that bypasses potential hanging issues by testing
the bridge service with minimal configuration and no persistent storage.
*/

use std::{sync::Arc, time::Duration};
use tokio::time::timeout;
use wellbot_bridge::{
    config::Config,
    services::{
        bridge_service::BridgeService,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
    types::IncomingWhatsAppData,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    println!("🚀 Starting Simple Bridge Service Test");
    println!("======================================");

    let self_jid = "<EMAIL>";

    // Step 1: Create minimal configuration
    println!("\n⚙️ Step 1: Creating minimal configuration...");
    let mut config = Config::default();
    
    // Disable auto-save to avoid storage issues
    println!("   Disabling auto-save to avoid storage issues...");
    
    println!("✅ Configuration ready");

    // Step 2: Create JID authorization service with minimal config
    println!("\n🔐 Step 2: Creating JID authorization service...");
    
    let mut jid_config = JidAuthConfig::default();
    jid_config.auto_save_interval_secs = 0; // Disable auto-save
    jid_config.storage_path = std::path::PathBuf::from("/tmp/test_jids.json"); // Use temp path
    
    println!("   Using temporary storage path: {:?}", jid_config.storage_path);
    
    match timeout(Duration::from_secs(15), async {
        JidAuthorizationService::new(jid_config).await
    }).await {
        Ok(Ok(jid_service)) => {
            println!("✅ JID authorization service created");
            
            // Test adding JID without hanging
            println!("   Testing JID addition...");
            match timeout(Duration::from_secs(5), async {
                jid_service.add_jid(self_jid.to_string(), Some("Test User".to_string())).await
            }).await {
                Ok(Ok(_)) => {
                    println!("   ✅ JID added successfully");
                    
                    // Test authorization check
                    let is_authorized = jid_service.is_authorized(self_jid).await;
                    println!("   ✅ Authorization check: {}", is_authorized);
                }
                Ok(Err(e)) => {
                    println!("   ❌ JID addition failed: {}", e);
                    return Ok(());
                }
                Err(_) => {
                    println!("   ❌ JID addition timed out");
                    return Ok(());
                }
            }
            
            // Step 3: Create bridge service
            println!("\n🌉 Step 3: Creating bridge service...");
            match timeout(Duration::from_secs(20), async {
                BridgeService::new(config, Arc::new(jid_service)).await
            }).await {
                Ok(Ok(bridge_service)) => {
                    println!("✅ Bridge service created successfully!");
                    
                    // Step 4: Test message processing
                    println!("\n📨 Step 4: Testing message processing...");
                    
                    let test_message = IncomingWhatsAppData {
                        from: self_jid.to_string(),
                        message: "Hello, this is a simple test message".to_string(),
                        message_id: format!("simple_test_{}", chrono::Utc::now().timestamp()),
                        timestamp: chrono::Utc::now(),
                    };
                    
                    match timeout(Duration::from_secs(10), async {
                        bridge_service.process_whatsapp_message(test_message).await
                    }).await {
                        Ok(Ok(())) => {
                            println!("✅ Message processed successfully!");
                            println!("📱 Check your WhatsApp for any response");
                        }
                        Ok(Err(e)) => {
                            println!("⚠️ Message processing failed: {}", e);
                            println!("   This might be expected if AI service is not available");
                        }
                        Err(_) => {
                            println!("❌ Message processing timed out");
                        }
                    }
                    
                    // Step 5: Get statistics
                    println!("\n📊 Step 5: Getting statistics...");
                    let stats = bridge_service.get_stats().await;
                    println!("📈 Bridge Service Statistics:");
                    println!("   • Messages Processed: {}", stats.messages_processed);
                    println!("   • Messages Authorized: {}", stats.messages_authorized);
                    println!("   • Messages Unauthorized: {}", stats.messages_unauthorized);
                    println!("   • AI Requests Sent: {}", stats.ai_requests_sent);
                    println!("   • AI Responses Received: {}", stats.ai_responses_received);
                    println!("   • Errors Encountered: {}", stats.errors_encountered);
                    
                    // Step 6: Test unauthorized message
                    println!("\n🚫 Step 6: Testing unauthorized message...");
                    let unauthorized_message = IncomingWhatsAppData {
                        from: "<EMAIL>".to_string(),
                        message: "This should be rejected".to_string(),
                        message_id: format!("unauthorized_test_{}", chrono::Utc::now().timestamp()),
                        timestamp: chrono::Utc::now(),
                    };
                    
                    match bridge_service.process_whatsapp_message(unauthorized_message).await {
                        Ok(()) => {
                            println!("⚠️ Unexpected: Unauthorized message was processed");
                        }
                        Err(e) => {
                            println!("✅ Unauthorized message correctly rejected: {}", e);
                        }
                    }
                    
                    println!("\n🎉 Simple Test Summary");
                    println!("======================");
                    println!("✅ Bridge service initialization: SUCCESS");
                    println!("✅ JID authorization: SUCCESS");
                    println!("✅ Message processing: TESTED");
                    println!("✅ Unauthorized rejection: SUCCESS");
                    
                    if stats.messages_processed > 0 {
                        println!("✅ End-to-end workflow: WORKING");
                    } else {
                        println!("⚠️ End-to-end workflow: PARTIAL (check AI service)");
                    }
                    
                    println!("\n📱 Check your WhatsApp messages for any AI responses!");
                    
                }
                Ok(Err(e)) => {
                    println!("❌ Bridge service creation failed: {}", e);
                }
                Err(_) => {
                    println!("❌ Bridge service creation timed out");
                    println!("   This suggests an issue with:");
                    println!("   - WebSocket connection to chat-port");
                    println!("   - AI service connection");
                    println!("   - Background task initialization");
                }
            }
        }
        Ok(Err(e)) => {
            println!("❌ JID authorization service creation failed: {}", e);
        }
        Err(_) => {
            println!("❌ JID authorization service creation timed out");
            println!("   This suggests an issue with storage or file permissions");
        }
    }

    Ok(())
}
