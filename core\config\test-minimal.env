# Minimal Configuration for Basic Testing
# This configuration provides the bare minimum settings needed to run the service

# Core Service Configuration
HEALTH_PORT=8081
AI_SERVICE_BASE_URL=http://localhost:8080
CHAT_PORT_BASE_URL=ws://localhost:3000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=pretty

# Basic Health Check Settings
HEALTH_CHECK_INTERVAL_SECS=60
HEALTH_HISTORY_RETENTION_HOURS=1

# Simple Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT_SECS=60
CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3

# Minimal Alerting
ALERT_MIN_SEVERITY=error
ALERT_RATE_LIMIT_WINDOW_SECS=300
ALERT_RATE_LIMIT_MAX_ALERTS=5

# Basic Metrics Collection
METRICS_COLLECTION_INTERVAL_SECS=60
METRICS_HISTORY_RETENTION_HOURS=1

# Health Thresholds (Relaxed)
HEALTH_RESPONSE_TIME_WARNING_MS=5000
HEALTH_RESPONSE_TIME_CRITICAL_MS=10000
HEALTH_MEMORY_WARNING_PERCENT=80
HEALTH_MEMORY_CRITICAL_PERCENT=90
HEALTH_CPU_WARNING_PERCENT=70
HEALTH_CPU_CRITICAL_PERCENT=85
