/*!
# 🔄 Self Chat End-to-End Test

Test the complete WhatsApp message processing workflow using your own WhatsApp JID
for self-chat testing. This verifies:

1. Message reception from chat-port WebSocket
2. JID authorization for your own number
3. AI processing with Genuis service
4. Response delivery back to your WhatsApp

## Usage
```bash
cargo run --example self_chat_test
```
*/

use std::{sync::Arc, time::Duration};
use tokio::time::sleep;
use wellbot_bridge::{
    config::Config,
    services::{
        bridge_service::BridgeService,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
    types::{IncomingWhatsAppData, SendMessageRequest},
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt().init();

    println!("🔄 Starting Self Chat End-to-End Test");
    println!("=====================================");

    // Your WhatsApp JID (from chat-port health check)
    let self_jid = "<EMAIL>";

    println!("📱 Using self JID: {}", self_jid);

    // Step 1: Initialize configuration
    println!("\n⚙️ Step 1: Initializing configuration...");
    let config = Config::default();
    println!("✅ Configuration loaded");

    // Step 2: Initialize JID authorization service
    println!("\n🔐 Step 2: Setting up JID authorization...");
    let jid_config = JidAuthConfig::default();
    let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);

    // Add your JID to authorized list
    jid_authorization
        .add_jid(self_jid.to_string(), Some("Self Test User".to_string()))
        .await?;
    println!("✅ Added {} to authorized JIDs", self_jid);

    // Step 3: Initialize bridge service
    println!("\n🌉 Step 3: Initializing bridge service...");
    let bridge_service = Arc::new(BridgeService::new(config, jid_authorization.clone()).await?);
    println!("✅ Bridge service initialized");

    // Step 4: Check service health
    println!("\n🏥 Step 4: Checking service health...");
    match bridge_service.health_check().await {
        Ok(is_healthy) => {
            if is_healthy {
                println!("✅ All services healthy");
            } else {
                println!("⚠️ Some services may have issues");
            }
        }
        Err(e) => {
            println!("❌ Health check failed: {}", e);
        }
    }

    // Step 5: Test message processing
    println!("\n📨 Step 5: Testing message processing...");

    let test_messages = ["Hello, this is a test message from self chat"];

    for (i, message_text) in test_messages.iter().enumerate() {
        println!(
            "\n📤 Test {}/{}: Processing message...",
            i + 1,
            test_messages.len()
        );
        println!("💬 Message: {}", message_text);

        // Create test message
        let whatsapp_data = IncomingWhatsAppData {
            from: self_jid.to_string(),
            message: message_text.to_string(),
            message_id: format!("self_test_{}_{}", i, chrono::Utc::now().timestamp()),
            timestamp: chrono::Utc::now(),
        };

        // Process message
        match bridge_service.process_whatsapp_message(whatsapp_data).await {
            Ok(()) => {
                println!("✅ Message processed successfully");
                println!("📱 Check your WhatsApp for the AI response!");
            }
            Err(e) => {
                println!("❌ Message processing failed: {}", e);
            }
        }

        // Wait between messages
        if i < test_messages.len() - 1 {
            println!("⏳ Waiting 3 seconds before next message...");
            sleep(Duration::from_secs(3)).await;
        }
    }

    // Step 6: Test unauthorized JID
    println!("\n🚫 Step 6: Testing unauthorized JID rejection...");
    let unauthorized_jid = "<EMAIL>";

    let unauthorized_message = IncomingWhatsAppData {
        from: unauthorized_jid.to_string(),
        message: "This should be rejected".to_string(),
        message_id: format!("unauthorized_test_{}", chrono::Utc::now().timestamp()),
        timestamp: chrono::Utc::now(),
    };

    match bridge_service
        .process_whatsapp_message(unauthorized_message)
        .await
    {
        Ok(()) => {
            println!("⚠️ Unexpected: Unauthorized message was processed");
        }
        Err(e) => {
            println!("✅ Unauthorized message correctly rejected: {}", e);
        }
    }

    // Step 7: Display final statistics
    println!("\n📊 Step 7: Final statistics...");
    let stats = bridge_service.get_stats().await;
    println!("📈 Bridge Service Statistics:");
    println!("  • Messages Processed: {}", stats.messages_processed);
    println!("  • Messages Authorized: {}", stats.messages_authorized);
    println!("  • Messages Unauthorized: {}", stats.messages_unauthorized);
    println!("  • AI Requests Sent: {}", stats.ai_requests_sent);
    println!("  • AI Responses Received: {}", stats.ai_responses_received);
    println!("  • Errors Encountered: {}", stats.errors_encountered);

    // Step 8: Test direct message sending
    println!("\n📤 Step 8: Testing direct message sending...");

    // Get chat port client from bridge service (we'll need to access it)
    // For now, let's create a simple send test
    let send_request = SendMessageRequest {
        number: "201030320366".to_string(), // Your number without @s.whatsapp.net
        message: "🤖 Direct test message from Wellbot Bridge Service! End-to-end test completed successfully.".to_string(),
    };

    // We would need access to the chat_port_client to send this
    // For now, let's just show what would be sent
    println!(
        "📱 Would send message to {}: {}",
        send_request.number, send_request.message
    );

    // Final summary
    println!("\n🎉 Self Chat End-to-End Test Summary");
    println!("====================================");

    let workflow_success = stats.messages_processed > 0 && stats.messages_authorized > 0;

    if workflow_success {
        println!("✅ SUCCESS: End-to-end workflow is working!");
        println!("✅ Message Reception: Working");
        println!("✅ JID Authorization: Working");
        println!("✅ Message Processing: Working");

        if stats.ai_requests_sent > 0 {
            println!("✅ AI Integration: Working");
        } else {
            println!("⚠️ AI Integration: No requests sent (check AI service)");
        }

        if stats.ai_responses_received > 0 {
            println!("✅ Response Delivery: Working");
        } else {
            println!("⚠️ Response Delivery: No responses received");
        }
    } else {
        println!("❌ ISSUES DETECTED: Some components need attention");

        if stats.messages_processed == 0 {
            println!("❌ No messages were processed");
        }
        if stats.messages_authorized == 0 {
            println!("❌ No messages were authorized");
        }
    }

    println!("\n📱 Check your WhatsApp messages to see the AI responses!");
    println!("🔄 Test completed. Press Ctrl+C to exit.");

    // Keep the program running briefly to see any async responses
    sleep(Duration::from_secs(5)).await;

    Ok(())
}
