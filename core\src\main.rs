use anyhow::Result;
use axum::{Router, extract::State, http::StatusCode, middleware, response::Json, routing::get};
use std::{sync::Arc, time::Duration};
use tokio::{net::TcpListener, signal, time::timeout};
use tower::ServiceBuilder;
use tower_http::{cors::CorsLayer, timeout::TimeoutLayer, trace::TraceLayer};
use tracing::{debug, error, info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use wellbot_bridge::{
    config::Config,
    services::{
        bridge_service::BridgeService,
        health_checker::{
            self, CircuitBreakerStatus, HealthTrends, PerformanceMetrics, RecoveryReport,
            ServiceHealth,
        },
    },
};

/// Application state for HTTP handlers
#[derive(Debug, Clone)]
struct AppState {
    bridge_service: Arc<BridgeService>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    init_logging().await?;

    // Load and validate configuration
    let config = Config::from_env()?;
    info!("📋 Configuration loaded successfully");
    info!("   - Health port: {}", config.health.port);
    info!("   - AI service URL: {}", config.ai_service.base_url);
    info!("   - Chat-port URL: {}", config.chat_port.api_base_url);

    // Validate configuration
    if config.health.port == 0 {
        error!("❌ Invalid health port configuration");
        return Err(anyhow::anyhow!("Health port cannot be 0"));
    }

    // Initialize the bridge service
    info!("🔧 Initializing bridge service...");
    let bridge_service = Arc::new(BridgeService::new(config.clone()).await?);
    info!("✅ Bridge service initialized successfully");

    // Create application state
    let app_state = AppState {
        bridge_service: bridge_service.clone(),
    };

    // Start health check HTTP server
    let health_port = config.health.port;
    let health_app = create_health_app(app_state.clone());

    tokio::spawn(async move {
        let listener = match TcpListener::bind(format!("0.0.0.0:{}", health_port)).await {
            Ok(listener) => {
                info!("🏥 Health check server listening on port {}", health_port);
                listener
            }
            Err(e) => {
                error!("Failed to bind health check server: {}", e);
                return;
            }
        };

        if let Err(e) = axum::serve(listener, health_app).await {
            error!("Health check server error: {}", e);
        }
    });

    info!(
        "🚀 Starting Wellbot Bridge Service v{}",
        env!("CARGO_PKG_VERSION")
    );

    // Start the bridge service
    if let Err(e) = bridge_service.start().await {
        error!("❌ Failed to start bridge service: {}", e);
        return Err(e.into());
    }

    info!("✅ Wellbot Bridge Service started successfully");
    info!("🏥 Health endpoints available:");
    info!("   - GET /health - Comprehensive health status");
    info!("   - GET /health/ready - Readiness check");
    info!("   - GET /health/live - Liveness check");
    info!("   - GET /health/trends - Health trends analysis");
    info!("   - GET /health/metrics - Performance metrics");
    info!("   - GET /health/circuit-breakers - Circuit breaker status");
    info!("   - GET /health/recovery - Trigger manual recovery");

    // Wait for shutdown signals
    info!("🎯 Service is running. Press Ctrl+C to shutdown gracefully");

    let shutdown_signal = async {
        let ctrl_c = async {
            signal::ctrl_c()
                .await
                .expect("Failed to install Ctrl+C handler");
        };

        #[cfg(unix)]
        let terminate = async {
            signal::unix::signal(signal::unix::SignalKind::terminate())
                .expect("Failed to install signal handler")
                .recv()
                .await;
        };

        #[cfg(not(unix))]
        let terminate = std::future::pending::<()>();

        tokio::select! {
            _ = ctrl_c => {
                info!("🛑 Received Ctrl+C signal");
            },
            _ = terminate => {
                info!("🛑 Received terminate signal");
            },
        }
    };

    shutdown_signal.await;
    info!("🔄 Initiating graceful shutdown...");

    // Graceful shutdown with timeout
    match timeout(Duration::from_secs(30), bridge_service.stop()).await {
        Ok(Ok(())) => {
            info!("✅ Bridge service stopped gracefully");
        }
        Ok(Err(e)) => {
            error!("❌ Error during graceful shutdown: {}", e);
        }
        Err(_) => {
            error!("⏰ Graceful shutdown timed out, forcing exit");
        }
    }

    Ok(())
}

/// Create health check HTTP application with middleware
fn create_health_app(state: AppState) -> Router {
    Router::new()
        .route("/", get(health_info_handler))
        .route("/health", get(health_handler))
        .route("/health/ready", get(readiness_handler))
        .route("/health/live", get(liveness_handler))
        .route("/health/trends", get(health_trends_handler))
        .route("/health/metrics", get(performance_metrics_handler))
        .route(
            "/health/circuit-breakers",
            get(circuit_breaker_status_handler),
        )
        .route("/health/recovery", get(recovery_handler))
        .route("/health/history", get(health_history_handler))
        .route("/health/alerts", get(active_alerts_handler))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http().on_failure(
                    tower_http::trace::DefaultOnFailure::new().level(tracing::Level::DEBUG),
                ))
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
                .layer(CorsLayer::permissive())
                .layer(middleware::from_fn(add_health_headers)),
        )
        .with_state(state)
}

/// Health info endpoint handler - provides API documentation
async fn health_info_handler() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "service": "wellbot-bridge",
        "version": env!("CARGO_PKG_VERSION"),
        "description": "Wellbot Bridge Service Health Monitoring API",
        "endpoints": {
            "/health": "Comprehensive health status",
            "/health/ready": "Readiness check (Kubernetes compatible)",
            "/health/live": "Liveness check (Kubernetes compatible)",
            "/health/trends": "Health trends analysis",
            "/health/metrics": "Performance metrics",
            "/health/circuit-breakers": "Circuit breaker status",
            "/health/recovery": "Manual recovery trigger",
            "/health/history": "Health check history",
            "/health/alerts": "Active alerts"
        },
        "timestamp": chrono::Utc::now()
    }))
}

/// Health check endpoint handler
async fn health_handler(State(state): State<AppState>) -> Result<Json<ServiceHealth>, StatusCode> {
    debug!("🏥 Processing health check request");

    match timeout(
        Duration::from_secs(10),
        state.bridge_service.health_checker().perform_health_check(),
    )
    .await
    {
        Ok(Ok(health)) => {
            debug!("✅ Health check completed successfully");
            Ok(Json(health))
        }
        Ok(Err(e)) => {
            warn!("❌ Health check failed: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
        Err(_) => {
            error!("⏰ Health check timed out");
            Err(StatusCode::REQUEST_TIMEOUT)
        }
    }
}

/// Readiness check endpoint handler
async fn readiness_handler(State(state): State<AppState>) -> (StatusCode, Json<serde_json::Value>) {
    let health = state
        .bridge_service
        .health_checker()
        .get_current_health()
        .await;

    let is_ready = matches!(health.status, health_checker::HealthStatus::Healthy);
    let status_code = if is_ready {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };

    let response = serde_json::json!({
        "ready": is_ready,
        "status": format!("{:?}", health.status),
        "timestamp": chrono::Utc::now(),
        "service": "wellbot-bridge"
    });

    debug!(
        "🔍 Readiness check: ready={}, status={:?}",
        is_ready, health.status
    );
    (status_code, Json(response))
}

/// Liveness check endpoint handler
async fn liveness_handler() -> Json<serde_json::Value> {
    // Simple liveness check - if we can respond, we're alive
    debug!("💓 Liveness check: service is alive");
    Json(serde_json::json!({
        "alive": true,
        "timestamp": chrono::Utc::now(),
        "service": "wellbot-bridge"
    }))
}

/// Health trends endpoint handler
async fn health_trends_handler(
    State(state): State<AppState>,
) -> Result<Json<HealthTrends>, StatusCode> {
    let trends = state.bridge_service.get_health_trends().await;
    Ok(Json(trends))
}

/// Performance metrics endpoint handler
async fn performance_metrics_handler(
    State(state): State<AppState>,
) -> Result<Json<PerformanceMetrics>, StatusCode> {
    let metrics = state.bridge_service.get_performance_metrics().await;
    Ok(Json(metrics))
}

/// Circuit breaker status endpoint handler
async fn circuit_breaker_status_handler(
    State(state): State<AppState>,
) -> Result<Json<std::collections::HashMap<String, CircuitBreakerStatus>>, StatusCode> {
    let status = state.bridge_service.get_circuit_breaker_status().await;
    Ok(Json(status))
}

/// Recovery endpoint handler - triggers manual recovery
async fn recovery_handler(
    State(state): State<AppState>,
) -> Result<Json<RecoveryReport>, StatusCode> {
    info!("🔄 Manual recovery requested");

    match timeout(
        Duration::from_secs(30),
        state.bridge_service.attempt_recovery(),
    )
    .await
    {
        Ok(Ok(report)) => {
            info!(
                "✅ Manual recovery completed: {} components attempted, {} successful",
                report.attempted_components.len(),
                report.successful_recoveries.len()
            );
            Ok(Json(report))
        }
        Ok(Err(e)) => {
            error!("❌ Manual recovery failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
        Err(_) => {
            error!("⏰ Manual recovery timed out");
            Err(StatusCode::REQUEST_TIMEOUT)
        }
    }
}

/// Health history endpoint handler
async fn health_history_handler(
    State(state): State<AppState>,
) -> Result<Json<Vec<ServiceHealth>>, StatusCode> {
    let history = state
        .bridge_service
        .health_checker()
        .get_health_history()
        .await;
    Ok(Json(history))
}

/// Active alerts endpoint handler
async fn active_alerts_handler(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let alerts = state
        .bridge_service
        .health_checker()
        .get_active_alerts()
        .await;
    let alert_count = alerts.len();

    Ok(Json(serde_json::json!({
        "active_alerts": alerts,
        "total_count": alert_count,
        "timestamp": chrono::Utc::now()
    })))
}

/// Middleware to add health-specific headers
async fn add_health_headers(
    request: axum::http::Request<axum::body::Body>,
    next: axum::middleware::Next,
) -> axum::response::Response {
    let mut response = next.run(request).await;

    let headers = response.headers_mut();
    headers.insert("X-Health-API-Version", "1.0".parse().unwrap());
    headers.insert("X-Service-Name", "wellbot-bridge".parse().unwrap());
    headers.insert(
        "X-Service-Version",
        env!("CARGO_PKG_VERSION").parse().unwrap(),
    );
    headers.insert(
        "Cache-Control",
        "no-cache, no-store, must-revalidate".parse().unwrap(),
    );

    response
}

/// Initialize structured logging based on configuration
async fn init_logging() -> Result<()> {
    // Load logging configuration from environment
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    let log_format = std::env::var("LOG_FORMAT").unwrap_or_else(|_| "pretty".to_string());
    let log_file = std::env::var("LOG_FILE").ok();

    // Create environment filter with appropriate levels for different modules
    let env_filter = format!(
        "wellbot_bridge={},tower_http=debug,axum=info,hyper=info,tokio=info",
        log_level
    );

    let subscriber = tracing_subscriber::registry().with(
        tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| env_filter.into()),
    );

    // Configure output format
    match log_format.as_str() {
        "json" => {
            let layer = tracing_subscriber::fmt::layer()
                .json()
                .with_target(true)
                .with_thread_ids(true)
                .with_thread_names(true)
                .with_file(true)
                .with_line_number(true);

            if let Some(file_path) = log_file {
                let file = std::fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(&file_path)?;
                subscriber.with(layer.with_writer(file)).init();
                info!(
                    "📝 JSON logging initialized - Level: {}, File: {}",
                    log_level, file_path
                );
            } else {
                subscriber.with(layer).init();
                info!("📝 JSON logging initialized - Level: {}", log_level);
            }
        }
        "compact" => {
            let layer = tracing_subscriber::fmt::layer()
                .compact()
                .with_target(false)
                .with_thread_ids(false);

            if let Some(file_path) = log_file {
                let file = std::fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(&file_path)?;
                subscriber.with(layer.with_writer(file)).init();
                info!(
                    "📝 Compact logging initialized - Level: {}, File: {}",
                    log_level, file_path
                );
            } else {
                subscriber.with(layer).init();
                info!("📝 Compact logging initialized - Level: {}", log_level);
            }
        }
        _ => {
            // Pretty format (default)
            let layer = tracing_subscriber::fmt::layer()
                .with_target(true)
                .with_thread_ids(false)
                .with_file(false)
                .with_line_number(false);

            if let Some(file_path) = log_file {
                let file = std::fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(&file_path)?;
                subscriber.with(layer.with_writer(file)).init();
                info!(
                    "📝 Pretty logging initialized - Level: {}, File: {}",
                    log_level, file_path
                );
            } else {
                subscriber.with(layer).init();
                info!("📝 Pretty logging initialized - Level: {}", log_level);
            }
        }
    }

    // Log startup banner
    info!("🚀 Wellbot Bridge Service v{}", env!("CARGO_PKG_VERSION"));
    info!("� Package: {}", env!("CARGO_PKG_NAME"));
    info!("� Authors: <AUTHORS>
    info!("📝 Description: {}", env!("CARGO_PKG_DESCRIPTION"));

    Ok(())
}
