#!/usr/bin/env python3
"""
Simple WebSocket test runner for Wellbot Bridge Service

This script provides easy-to-use commands for running WebSocket connection tests
with different configurations and scenarios.

Usage Examples:
    python run_websocket_tests.py                    # Basic WebSocket tests
    python run_websocket_tests.py --verbose          # Verbose output
    python run_websocket_tests.py --save-report      # Save detailed report
    python run_websocket_tests.py --concurrent 5     # Test 5 concurrent connections
"""

import sys
import os
import subprocess
import argparse
import asyncio
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import websockets
        print("✅ websockets library is available")
        return True
    except ImportError:
        print("❌ websockets library not found")
        print("Install with: pip install websockets")
        return False


def check_chat_port_service(chat_port_url="ws://localhost:8081/ws"):
    """Check if the chat-port service is running"""
    try:
        import asyncio
        import websockets
        
        async def test_connection():
            try:
                async with websockets.connect(chat_port_url, timeout=5.0) as websocket:
                    return True
            except Exception:
                return False
        
        result = asyncio.run(test_connection())
        if result:
            print(f"✅ Chat-port service is running at {chat_port_url}")
            return True
        else:
            print(f"❌ Chat-port service not reachable at {chat_port_url}")
            return False
    except Exception as e:
        print(f"❌ Error checking chat-port service: {e}")
        return False


def run_websocket_tests(args):
    """Run WebSocket connection tests"""
    cmd = [sys.executable, "websocket_connection_tests.py"]
    
    if args.chat_port_url:
        cmd.extend(["--chat-port-url", args.chat_port_url])
    if args.bridge_url:
        cmd.extend(["--bridge-url", args.bridge_url])
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")
    if args.concurrent:
        cmd.extend(["--concurrent", str(args.concurrent)])
    
    print(f"🚀 Running WebSocket tests: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge WebSocket Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_websocket_tests.py                    # Basic WebSocket tests
  python run_websocket_tests.py --verbose          # Verbose output
  python run_websocket_tests.py --save-report      # Save detailed report
  python run_websocket_tests.py --concurrent 5     # Test 5 concurrent connections
  
  # Custom service URLs
  python run_websocket_tests.py --chat-port-url ws://localhost:8081/ws
  
  # Full test with reporting
  python run_websocket_tests.py --verbose --save-report --concurrent 10
        """
    )
    
    parser.add_argument("--chat-port-url", default="ws://localhost:8081/ws",
                       help="Chat-port WebSocket URL (default: ws://localhost:8081/ws)")
    parser.add_argument("--bridge-url", default="ws://localhost:3030/ws",
                       help="Bridge WebSocket URL (default: ws://localhost:3030/ws)")
    parser.add_argument("--timeout", type=float, default=30.0,
                       help="Connection timeout in seconds (default: 30.0)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")
    parser.add_argument("--save-report", action="store_true",
                       help="Save detailed report to JSON file")
    parser.add_argument("--concurrent", type=int, default=3,
                       help="Number of concurrent connections to test (default: 3)")
    
    # Additional options
    parser.add_argument("--skip-checks", action="store_true",
                       help="Skip dependency and service checks")
    
    args = parser.parse_args()
    
    print("🔌 Wellbot Bridge WebSocket Test Runner")
    print("=" * 50)
    
    # Check dependencies and service status
    if not args.skip_checks:
        if not check_dependencies():
            return 1
        
        if not check_chat_port_service(args.chat_port_url):
            print("Make sure the chat-port service is running:")
            print("  cd chat-port && go run cmd/main.go")
            return 1
    
    # Run WebSocket tests
    try:
        result = run_websocket_tests(args)
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
