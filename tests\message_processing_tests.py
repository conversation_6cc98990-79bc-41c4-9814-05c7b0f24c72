#!/usr/bin/env python3
"""
Comprehensive Message Processing Tests for Wellbot Bridge Service

This module provides manual testing capabilities for message processing,
AI integration, JID filtering, and WhatsApp message handling using
Python requests library.

Usage:
    python message_processing_tests.py
    python message_processing_tests.py --bridge-url http://localhost:3030
    python message_processing_tests.py --verbose --timeout 30
"""

import argparse
import json
import time
import sys
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable
import requests
from dataclasses import dataclass
from enum import Enum
import asyncio
import websockets


class TestResult(Enum):
    """Test result status"""

    PASS = "PASS"
    FAIL = "FAIL"
    WARN = "WARN"
    SKIP = "SKIP"


@dataclass
class MessageTestCase:
    """Message processing test case definition"""

    name: str
    description: str
    test_func: Callable
    timeout: float = 30.0
    requires_ai: bool = False
    requires_chat_port: bool = False
    setup_func: Optional[Callable] = None
    cleanup_func: Optional[Callable] = None


@dataclass
class MessageTestReport:
    """Message processing test execution report"""

    test_name: str
    result: TestResult
    duration_ms: float
    messages_processed: int
    ai_requests_made: int
    jid_checks_performed: int
    error_message: Optional[str]
    response_data: Optional[Dict]
    timestamp: datetime


class MessageProcessingTester:
    """Comprehensive message processing testing suite"""

    def __init__(
        self,
        bridge_url: str = "http://localhost:3030",
        chat_port_url: str = "ws://localhost:8081/ws",
        ai_service_url: str = "http://localhost:8000",
        timeout: float = 30.0,
        verbose: bool = False,
    ):
        self.bridge_url = bridge_url.rstrip("/")
        self.chat_port_url = chat_port_url
        self.ai_service_url = ai_service_url.rstrip("/")
        self.timeout = timeout
        self.verbose = verbose
        self.test_reports: List[MessageTestReport] = []
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Wellbot-Message-Tester/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json",
            }
        )

    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp and color coding"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if level == "ERROR":
            print(f"\033[91m[{timestamp}] [FAIL] {message}\033[0m")
        elif level == "WARN":
            print(f"\033[93m[{timestamp}] [WARN] {message}\033[0m")
        elif level == "PASS":
            print(f"\033[92m[{timestamp}] [PASS] {message}\033[0m")
        elif level == "INFO":
            print(f"\033[94m[{timestamp}] [INFO] {message}\033[0m")
        else:
            print(f"[{timestamp}] [{level}] {message}")

    def create_test_whatsapp_message(
        self, from_jid: str, message: str, message_id: Optional[str] = None
    ) -> Dict:
        """Create a test WhatsApp message structure"""
        return {
            "from": from_jid,
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message_id": message_id or str(uuid.uuid4()),
        }

    def create_websocket_message(self, msg_type: str, data: Any) -> Dict:
        """Create a WebSocket message for chat-port"""
        return {
            "type": msg_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data,
        }

    async def test_jid_authorization_check(self) -> MessageTestReport:
        """Test JID authorization checking"""
        start_time = time.time()
        messages_processed = 0
        jid_checks = 0

        try:
            self.log("Testing JID authorization checking")

            # Test authorized JID
            authorized_jid = "<EMAIL>"
            test_message = self.create_test_whatsapp_message(
                authorized_jid, "Test message from authorized JID"
            )

            # Send via WebSocket to simulate real message flow
            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                _welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                # Send incoming WhatsApp message
                ws_message = self.create_websocket_message(
                    "incoming_whatsapp", test_message
                )
                await websocket.send(json.dumps(ws_message))
                messages_processed += 1
                jid_checks += 1

                self.log(f"✅ Sent message from authorized JID: {authorized_jid}")

                # Test unauthorized JID
                unauthorized_jid = "<EMAIL>"
                test_message_unauth = self.create_test_whatsapp_message(
                    unauthorized_jid, "Test message from unauthorized JID"
                )

                ws_message_unauth = self.create_websocket_message(
                    "incoming_whatsapp", test_message_unauth
                )
                await websocket.send(json.dumps(ws_message_unauth))
                messages_processed += 1
                jid_checks += 1

                self.log(f"✅ Sent message from unauthorized JID: {unauthorized_jid}")

                # Wait for any responses or processing
                await asyncio.sleep(2)

            return MessageTestReport(
                test_name="JID Authorization Check",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=jid_checks,
                error_message=None,
                response_data={
                    "authorized_jid": authorized_jid,
                    "unauthorized_jid": unauthorized_jid,
                },
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="JID Authorization Check",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=jid_checks,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_ai_integration(self) -> MessageTestReport:
        """Test AI service integration"""
        start_time = time.time()
        messages_processed = 0
        ai_requests = 0

        try:
            self.log("Testing AI service integration")

            # First check if AI service is available
            try:
                ai_health_response = self.session.get(
                    f"{self.ai_service_url}/health", timeout=5
                )
                if ai_health_response.status_code != 200:
                    self.log("⚠️ AI service not available, skipping AI integration test")
                    return MessageTestReport(
                        test_name="AI Integration",
                        result=TestResult.SKIP,
                        duration_ms=(time.time() - start_time) * 1000,
                        messages_processed=0,
                        ai_requests_made=0,
                        jid_checks_performed=0,
                        error_message="AI service not available",
                        response_data=None,
                        timestamp=datetime.now(),
                    )
            except Exception:
                self.log("⚠️ AI service unreachable, skipping AI integration test")
                return MessageTestReport(
                    test_name="AI Integration",
                    result=TestResult.SKIP,
                    duration_ms=(time.time() - start_time) * 1000,
                    messages_processed=0,
                    ai_requests_made=0,
                    jid_checks_performed=0,
                    error_message="AI service unreachable",
                    response_data=None,
                    timestamp=datetime.now(),
                )

            # Test AI explanation endpoint directly
            ai_request = {
                "prompt": "Explain what is machine learning in simple terms",
                "temperature": 0.7,
                "max_tokens": 100,
            }

            ai_response = self.session.post(
                f"{self.ai_service_url}/api/v1/explanations",
                json=ai_request,
                timeout=self.timeout,
            )
            ai_requests += 1

            if ai_response.status_code == 200:
                ai_data = ai_response.json()
                self.log("✅ AI service responded successfully")
                if self.verbose:
                    self.log(f"AI Response: {ai_data.get('content', '')[:100]}...")
            else:
                self.log(f"⚠️ AI service returned status: {ai_response.status_code}")

            # Test message processing with AI via WebSocket
            authorized_jid = "<EMAIL>"
            test_message = self.create_test_whatsapp_message(
                authorized_jid, "What is artificial intelligence?"
            )

            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                # Send message that should trigger AI processing
                ws_message = self.create_websocket_message(
                    "incoming_whatsapp", test_message
                )
                await websocket.send(json.dumps(ws_message))
                messages_processed += 1

                self.log("✅ Sent message for AI processing")

                # Wait for processing
                await asyncio.sleep(5)

            return MessageTestReport(
                test_name="AI Integration",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=ai_requests,
                jid_checks_performed=0,
                error_message=None,
                response_data={"ai_service_status": ai_response.status_code},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="AI Integration",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=ai_requests if "ai_requests" in locals() else 0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_rate_limiting(self) -> MessageTestReport:
        """Test message rate limiting functionality"""
        start_time = time.time()
        messages_processed = 0

        try:
            self.log("Testing rate limiting")

            authorized_jid = "<EMAIL>"

            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                _welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                # Send multiple messages rapidly to test rate limiting
                for i in range(10):
                    test_message = self.create_test_whatsapp_message(
                        authorized_jid, f"Rate limit test message {i + 1}"
                    )

                    ws_message = self.create_websocket_message(
                        "incoming_whatsapp", test_message
                    )
                    await websocket.send(json.dumps(ws_message))
                    messages_processed += 1

                    if self.verbose:
                        self.log(f"📤 Sent rate limit test message {i + 1}")

                    # Very brief pause to simulate rapid messages
                    await asyncio.sleep(0.1)

                self.log(
                    f"✅ Sent {messages_processed} messages for rate limiting test"
                )

                # Wait for processing
                await asyncio.sleep(3)

            return MessageTestReport(
                test_name="Rate Limiting",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=None,
                response_data={"messages_sent": messages_processed},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="Rate Limiting",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_concurrent_message_processing(self) -> MessageTestReport:
        """Test concurrent message processing"""
        start_time = time.time()
        total_messages_processed = 0

        try:
            self.log("Testing concurrent message processing")

            async def send_messages_batch(batch_id: int, num_messages: int):
                messages_sent = 0
                try:
                    async with websockets.connect(self.chat_port_url) as websocket:
                        # Wait for welcome message
                        _welcome_msg = await asyncio.wait_for(
                            websocket.recv(), timeout=5.0
                        )

                        for i in range(num_messages):
                            test_message = self.create_test_whatsapp_message(
                                "<EMAIL>",
                                f"Concurrent test batch {batch_id} message {i + 1}",
                            )

                            ws_message = self.create_websocket_message(
                                "incoming_whatsapp", test_message
                            )
                            await websocket.send(json.dumps(ws_message))
                            messages_sent += 1

                            await asyncio.sleep(0.2)  # Brief pause between messages

                        self.log(f"✅ Batch {batch_id} sent {messages_sent} messages")
                        return messages_sent

                except Exception as e:
                    self.log(f"❌ Batch {batch_id} failed: {e}")
                    return messages_sent

            # Run 3 concurrent batches of 3 messages each
            tasks = [send_messages_batch(i, 3) for i in range(3)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Aggregate results
            for result in results:
                if isinstance(result, int):
                    total_messages_processed += result

            success_rate = (
                len([r for r in results if isinstance(r, int)]) / len(tasks) * 100
            )

            self.log(f"📊 Concurrent test completed: {success_rate:.1f}% success rate")
            self.log(f"📊 Total messages processed: {total_messages_processed}")

            result_status = TestResult.PASS if success_rate >= 80 else TestResult.WARN

            return MessageTestReport(
                test_name="Concurrent Message Processing",
                result=result_status,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=total_messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=f"Success rate: {success_rate:.1f}%"
                if success_rate < 100
                else None,
                response_data={"success_rate": success_rate, "batches": len(tasks)},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="Concurrent Message Processing",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=total_messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_message_context_tracking(self) -> MessageTestReport:
        """Test message context and processing tracking"""
        start_time = time.time()
        messages_processed = 0

        try:
            self.log("Testing message context tracking")

            # Test different message types and contexts
            test_scenarios = [
                ("<EMAIL>", "Hello, how are you?", "greeting"),
                ("<EMAIL>", "What is the weather like?", "question"),
                ("<EMAIL>", "Thank you for your help!", "gratitude"),
                (
                    "<EMAIL>",
                    "Can you explain quantum physics?",
                    "complex_question",
                ),
            ]

            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                _welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                for jid, message_text, context_type in test_scenarios:
                    test_message = self.create_test_whatsapp_message(jid, message_text)
                    test_message["context_type"] = context_type  # Add context metadata

                    ws_message = self.create_websocket_message(
                        "incoming_whatsapp", test_message
                    )
                    await websocket.send(json.dumps(ws_message))
                    messages_processed += 1

                    self.log(f"📤 Sent {context_type} message: {message_text[:30]}...")

                    # Pause between different message types
                    await asyncio.sleep(1)

                # Wait for processing
                await asyncio.sleep(3)

            return MessageTestReport(
                test_name="Message Context Tracking",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=None,
                response_data={"scenarios_tested": len(test_scenarios)},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="Message Context Tracking",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_error_handling_and_recovery(self) -> MessageTestReport:
        """Test error handling and recovery mechanisms"""
        start_time = time.time()
        messages_processed = 0

        try:
            self.log("Testing error handling and recovery")

            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                _welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                # Test various error scenarios
                error_scenarios = [
                    # Very long message (potential processing issues)
                    ("<EMAIL>", "A" * 5000, "long_message"),
                    # Special characters and emojis
                    (
                        "<EMAIL>",
                        "🚀🎉💻🤖 Special chars: @#$%^&*()[]{}|\\:;\"'<>,.?/",
                        "special_chars",
                    ),
                    # Non-English text
                    (
                        "<EMAIL>",
                        "こんにちは世界 مرحبا بالعالم Здравствуй мир",
                        "unicode_text",
                    ),
                    # Empty message (edge case)
                    ("<EMAIL>", "", "empty_message"),
                    # Malformed JID
                    (
                        "invalid-jid-format",
                        "Test message with invalid JID",
                        "invalid_jid",
                    ),
                ]

                for jid, message_text, scenario_type in error_scenarios:
                    try:
                        test_message = self.create_test_whatsapp_message(
                            jid, message_text
                        )
                        test_message["scenario_type"] = scenario_type

                        ws_message = self.create_websocket_message(
                            "incoming_whatsapp", test_message
                        )
                        await websocket.send(json.dumps(ws_message))
                        messages_processed += 1

                        self.log(f"📤 Sent {scenario_type} test")

                        # Pause between error scenarios
                        await asyncio.sleep(1)

                    except Exception as e:
                        self.log(f"⚠️ Error in {scenario_type} scenario: {e}")

                # Send a normal message to test recovery
                recovery_message = self.create_test_whatsapp_message(
                    "<EMAIL>", "Normal message to test recovery"
                )

                ws_message = self.create_websocket_message(
                    "incoming_whatsapp", recovery_message
                )
                await websocket.send(json.dumps(ws_message))
                messages_processed += 1

                self.log("✅ Sent recovery test message")

                # Wait for processing
                await asyncio.sleep(3)

            return MessageTestReport(
                test_name="Error Handling and Recovery",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=None,
                response_data={"error_scenarios_tested": len(error_scenarios)},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="Error Handling and Recovery",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def test_message_validation(self) -> MessageTestReport:
        """Test message validation and error handling"""
        start_time = time.time()
        messages_processed = 0

        try:
            self.log("Testing message validation")

            async with websockets.connect(self.chat_port_url) as websocket:
                # Wait for welcome message
                _welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)

                # Test invalid message structures
                invalid_messages = [
                    # Missing required fields
                    {"from": "<EMAIL>"},
                    {"message": "test message"},
                    {"timestamp": datetime.now(timezone.utc).isoformat()},
                    # Invalid field types
                    {
                        "from": 123,
                        "message": "test",
                        "timestamp": "invalid",
                        "message_id": "test",
                    },
                    {
                        "from": "<EMAIL>",
                        "message": None,
                        "timestamp": "invalid",
                        "message_id": "test",
                    },
                    # Empty values
                    {
                        "from": "",
                        "message": "",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "message_id": "",
                    },
                ]

                for i, invalid_msg in enumerate(invalid_messages):
                    try:
                        ws_message = self.create_websocket_message(
                            "incoming_whatsapp", invalid_msg
                        )
                        await websocket.send(json.dumps(ws_message))
                        messages_processed += 1
                        self.log(f"📤 Sent invalid message {i + 1}")

                        # Brief pause between messages
                        await asyncio.sleep(0.5)

                    except Exception as e:
                        self.log(f"⚠️ Error sending invalid message {i + 1}: {e}")

                # Test valid message for comparison
                valid_message = self.create_test_whatsapp_message(
                    "<EMAIL>", "This is a valid test message"
                )

                ws_message = self.create_websocket_message(
                    "incoming_whatsapp", valid_message
                )
                await websocket.send(json.dumps(ws_message))
                messages_processed += 1
                self.log("✅ Sent valid message for comparison")

                # Wait for processing
                await asyncio.sleep(2)

            return MessageTestReport(
                test_name="Message Validation",
                result=TestResult.PASS,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=None,
                response_data={"invalid_messages_tested": len(invalid_messages)},
                timestamp=datetime.now(),
            )

        except Exception as e:
            return MessageTestReport(
                test_name="Message Validation",
                result=TestResult.FAIL,
                duration_ms=(time.time() - start_time) * 1000,
                messages_processed=messages_processed,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def run_single_test(self, test_func: Callable) -> MessageTestReport:
        """Run a single message processing test"""
        try:
            return await test_func()
        except Exception as e:
            return MessageTestReport(
                test_name=test_func.__name__,
                result=TestResult.FAIL,
                duration_ms=0,
                messages_processed=0,
                ai_requests_made=0,
                jid_checks_performed=0,
                error_message=str(e),
                response_data=None,
                timestamp=datetime.now(),
            )

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all message processing tests"""
        self.log("🚀 Starting Wellbot Bridge Message Processing Tests")
        self.log(f"Bridge URL: {self.bridge_url}")
        self.log(f"Chat-port URL: {self.chat_port_url}")
        self.log(f"AI Service URL: {self.ai_service_url}")
        self.log(f"Timeout: {self.timeout}s")

        start_time = time.time()

        # Define test cases
        test_cases = [
            ("JID Authorization", self.test_jid_authorization_check),
            ("AI Integration", self.test_ai_integration),
            ("Message Validation", self.test_message_validation),
            ("Rate Limiting", self.test_rate_limiting),
            ("Concurrent Processing", self.test_concurrent_message_processing),
            ("Context Tracking", self.test_message_context_tracking),
            ("Error Handling", self.test_error_handling_and_recovery),
        ]

        # Run tests
        for test_name, test_func in test_cases:
            self.log(f"Running: {test_name}")

            report = await self.run_single_test(test_func)
            report.test_name = test_name
            self.test_reports.append(report)

            # Log result
            if report.result == TestResult.PASS:
                self.log(f"✅ {test_name} - {report.duration_ms:.1f}ms", "PASS")
            elif report.result == TestResult.WARN:
                self.log(f"⚠️ {test_name} - {report.error_message}", "WARN")
            elif report.result == TestResult.SKIP:
                self.log(f"⏭️ {test_name} - {report.error_message}", "INFO")
            else:
                self.log(f"❌ {test_name} - {report.error_message}", "ERROR")

            if self.verbose:
                self.log(
                    f"Messages: {report.messages_processed} processed, AI: {report.ai_requests_made} requests"
                )

            await asyncio.sleep(1)  # Brief pause between tests

        total_time = time.time() - start_time
        self.log(f"🏁 All message processing tests completed in {total_time:.1f}s")

        return self.generate_summary()

    def generate_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        if not self.test_reports:
            return {"error": "No tests executed"}

        total_tests = len(self.test_reports)
        passed_tests = sum(1 for r in self.test_reports if r.result == TestResult.PASS)
        failed_tests = sum(1 for r in self.test_reports if r.result == TestResult.FAIL)
        warned_tests = sum(1 for r in self.test_reports if r.result == TestResult.WARN)
        skipped_tests = sum(1 for r in self.test_reports if r.result == TestResult.SKIP)

        total_messages_processed = sum(r.messages_processed for r in self.test_reports)
        total_ai_requests = sum(r.ai_requests_made for r in self.test_reports)
        total_jid_checks = sum(r.jid_checks_performed for r in self.test_reports)

        avg_duration = (
            sum(r.duration_ms for r in self.test_reports) / total_tests
            if total_tests > 0
            else 0
        )

        summary = {
            "test_execution": {
                "timestamp": datetime.now().isoformat(),
                "bridge_url": self.bridge_url,
                "chat_port_url": self.chat_port_url,
                "ai_service_url": self.ai_service_url,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "warned_tests": warned_tests,
                "skipped_tests": skipped_tests,
                "success_rate": (passed_tests / total_tests) * 100
                if total_tests > 0
                else 0,
            },
            "processing_stats": {
                "total_messages_processed": total_messages_processed,
                "total_ai_requests": total_ai_requests,
                "total_jid_checks": total_jid_checks,
                "avg_test_duration_ms": avg_duration,
            },
            "test_details": [
                {
                    "name": r.test_name,
                    "result": r.result.value,
                    "duration_ms": r.duration_ms,
                    "messages_processed": r.messages_processed,
                    "ai_requests_made": r.ai_requests_made,
                    "jid_checks_performed": r.jid_checks_performed,
                    "error_message": r.error_message,
                }
                for r in self.test_reports
            ],
        }

        # Print summary
        self.log("📋 Message Processing Test Summary:")
        self.log(
            f"   Total: {total_tests}, Passed: {passed_tests}, Failed: {failed_tests}, Warned: {warned_tests}, Skipped: {skipped_tests}"
        )
        self.log(f"   Success Rate: {summary['test_execution']['success_rate']:.1f}%")
        self.log(f"   Messages Processed: {total_messages_processed}")
        self.log(f"   AI Requests: {total_ai_requests}")
        self.log(f"   JID Checks: {total_jid_checks}")
        self.log(f"   Avg Duration: {avg_duration:.1f}ms")

        return summary

    def save_report(self, filename: Optional[str] = None) -> str:
        """Save detailed test report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"message_processing_test_report_{timestamp}.json"

        summary = self.generate_summary()

        with open(filename, "w") as f:
            json.dump(summary, f, indent=2, default=str)

        self.log(f"📄 Report saved to: {filename}")
        return filename


async def main():
    """Main test execution function"""
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge Message Processing Tester"
    )
    parser.add_argument(
        "--bridge-url",
        default="http://localhost:3030",
        help="Bridge service URL (default: http://localhost:3030)",
    )
    parser.add_argument(
        "--chat-port-url",
        default="ws://localhost:8081/ws",
        help="Chat-port WebSocket URL (default: ws://localhost:8081/ws)",
    )
    parser.add_argument(
        "--ai-service-url",
        default="http://localhost:8000",
        help="AI service URL (default: http://localhost:8000)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=30.0,
        help="Request timeout in seconds (default: 30.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )

    args = parser.parse_args()

    # Initialize tester
    tester = MessageProcessingTester(
        bridge_url=args.bridge_url,
        chat_port_url=args.chat_port_url,
        ai_service_url=args.ai_service_url,
        timeout=args.timeout,
        verbose=args.verbose,
    )

    try:
        # Run message processing tests
        summary = await tester.run_all_tests()

        # Save report if requested
        if args.save_report:
            tester.save_report()

        # Exit with appropriate code
        failed_tests = summary["test_execution"]["failed_tests"]
        sys.exit(1 if failed_tests > 0 else 0)

    except KeyboardInterrupt:
        tester.log("🛑 Tests interrupted by user", "WARN")
        sys.exit(130)
    except Exception as e:
        tester.log(f"💥 Unexpected error: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
