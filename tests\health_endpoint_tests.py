#!/usr/bin/env python3
"""
Comprehensive Health Endpoint Tests for Wellbot Bridge Service

This module provides manual testing capabilities for all health check endpoints
using Python requests library. Tests include response validation, performance
checks, and error scenario handling.

Usage:
    python health_endpoint_tests.py
    python health_endpoint_tests.py --base-url http://localhost:3030
    python health_endpoint_tests.py --verbose --timeout 30
"""

import argparse
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Callable
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError
import concurrent.futures
from dataclasses import dataclass
from enum import Enum


class TestResult(Enum):
    """Test result status"""

    PASS = "PASS"
    FAIL = "FAIL"
    WARN = "WARN"
    SKIP = "SKIP"


@dataclass
class TestCase:
    """Individual test case definition"""

    name: str
    endpoint: str
    method: str = "GET"
    expected_status: int = 200
    timeout: float = 10.0
    description: str = ""
    validation_func: Optional[Callable] = None
    headers: Optional[Dict[str, str]] = None
    data: Optional[Dict] = None


@dataclass
class TestReport:
    """Test execution report"""

    test_name: str
    endpoint: str
    result: TestResult
    status_code: Optional[int]
    response_time_ms: Optional[float]
    response_size: Optional[int]
    error_message: Optional[str]
    response_data: Optional[Dict]
    timestamp: datetime


class HealthEndpointTester:
    """Comprehensive health endpoint testing suite"""

    def __init__(
        self,
        base_url: str = "http://localhost:3030",
        timeout: float = 10.0,
        verbose: bool = False,
    ):
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.verbose = verbose
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Wellbot-Health-Tester/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json",
            }
        )
        self.test_reports: List[TestReport] = []

    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        prefix = f"[{timestamp}] [{level}]"

        if level == "ERROR":
            print(f"\033[91m{prefix} {message}\033[0m")
        elif level == "WARN":
            print(f"\033[93m{prefix} {message}\033[0m")
        elif level == "PASS":
            print(f"\033[92m{prefix} {message}\033[0m")
        elif level == "INFO":
            print(f"\033[94m{prefix} {message}\033[0m")
        else:
            print(f"{prefix} {message}")

    def validate_basic_health_response(self, response_data: Dict) -> Tuple[bool, str]:
        """Validate basic health endpoint response structure"""
        required_fields = [
            "service_name",
            "version",
            "status",
            "timestamp",
            "components",
        ]

        for field in required_fields:
            if field not in response_data:
                return False, f"Missing required field: {field}"

        # Validate status values (updated to match actual API responses)
        valid_statuses = ["Healthy", "Degraded", "Unhealthy", "Warning"]
        if response_data.get("status") not in valid_statuses:
            return False, f"Invalid status: {response_data.get('status')}"

        # Validate components structure
        components = response_data.get("components", [])
        if not isinstance(components, list):
            return False, "Components should be a list"

        for component in components:
            if not isinstance(component, dict):
                return False, "Each component should be a dictionary"

            required_component_fields = ["name", "status", "last_check"]
            for field in required_component_fields:
                if field not in component:
                    return False, f"Component missing field: {field}"

        return True, "Valid health response structure"

    def validate_readiness_response(self, response_data: Dict) -> Tuple[bool, str]:
        """Validate readiness endpoint response"""
        if "ready" not in response_data:
            return False, "Missing 'ready' field"

        if not isinstance(response_data["ready"], bool):
            return False, "'ready' field should be boolean"

        return True, "Valid readiness response"

    def validate_liveness_response(self, response_data: Dict) -> Tuple[bool, str]:
        """Validate liveness endpoint response"""
        if "alive" not in response_data:
            return False, "Missing 'alive' field"

        if not isinstance(response_data["alive"], bool):
            return False, "'alive' field should be boolean"

        return True, "Valid liveness response"

    def validate_metrics_response(self, response_data: Dict) -> Tuple[bool, str]:
        """Validate metrics endpoint response"""
        # Check if response contains any metrics data
        if not response_data:
            return False, "Empty metrics response"

        # Look for common metrics fields (flexible validation)
        metrics_indicators = [
            "system_metrics",
            "processing_stats",
            "uptime_seconds",
            "components",
            "service_name",
            "timestamp",
            "avg_memory_usage_percent",
            "max_memory_usage_percent",
            "avg_cpu_usage_percent",
            "max_cpu_usage_percent",
            "avg_response_time_ms",
            "total_requests",
            "error_rate",
        ]

        found_indicators = sum(
            1 for indicator in metrics_indicators if indicator in response_data
        )

        if found_indicators == 0:
            return False, "No recognizable metrics data found"

        return True, f"Valid metrics response with {found_indicators} metric indicators"

    def execute_test_case(self, test_case: TestCase) -> TestReport:
        """Execute a single test case"""
        start_time = time.time()
        url = f"{self.base_url}{test_case.endpoint}"

        try:
            if self.verbose:
                self.log(f"Testing {test_case.method} {url}")

            response = self.session.request(
                method=test_case.method,
                url=url,
                timeout=test_case.timeout,
                headers=test_case.headers,
                json=test_case.data,
            )

            response_time_ms = (time.time() - start_time) * 1000

            # Parse response data
            response_data = None
            try:
                response_data = response.json() if response.content else {}
            except json.JSONDecodeError:
                response_data = {"raw_content": response.text}

            # Determine test result
            result = TestResult.PASS
            error_message = None

            # Check status code
            if response.status_code != test_case.expected_status:
                result = TestResult.FAIL
                error_message = f"Expected status {test_case.expected_status}, got {response.status_code}"

            # Run custom validation if provided
            elif test_case.validation_func and response_data:
                is_valid, validation_message = test_case.validation_func(response_data)
                if not is_valid:
                    result = TestResult.FAIL
                    error_message = f"Validation failed: {validation_message}"

            # Check response time (warn if > 5 seconds)
            if response_time_ms > 5000:
                if result == TestResult.PASS:
                    result = TestResult.WARN
                    error_message = f"Slow response time: {response_time_ms:.1f}ms"

            return TestReport(
                test_name=test_case.name,
                endpoint=test_case.endpoint,
                result=result,
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                response_size=len(response.content) if response.content else 0,
                error_message=error_message,
                response_data=response_data,
                timestamp=datetime.now(),
            )

        except Timeout:
            return TestReport(
                test_name=test_case.name,
                endpoint=test_case.endpoint,
                result=TestResult.FAIL,
                status_code=None,
                response_time_ms=(time.time() - start_time) * 1000,
                response_size=None,
                error_message=f"Request timeout after {test_case.timeout}s",
                response_data=None,
                timestamp=datetime.now(),
            )

        except ConnectionError:
            return TestReport(
                test_name=test_case.name,
                endpoint=test_case.endpoint,
                result=TestResult.FAIL,
                status_code=None,
                response_time_ms=(time.time() - start_time) * 1000,
                response_size=None,
                error_message="Connection failed - is the service running?",
                response_data=None,
                timestamp=datetime.now(),
            )

        except RequestException as e:
            return TestReport(
                test_name=test_case.name,
                endpoint=test_case.endpoint,
                result=TestResult.FAIL,
                status_code=None,
                response_time_ms=(time.time() - start_time) * 1000,
                response_size=None,
                error_message=f"Request failed: {str(e)}",
                response_data=None,
                timestamp=datetime.now(),
            )

    def get_test_cases(self) -> List[TestCase]:
        """Define all health endpoint test cases"""
        return [
            TestCase(
                name="API Documentation",
                endpoint="/",
                description="Verify API documentation endpoint",
                validation_func=lambda data: (
                    "service" in data and "endpoints" in data,
                    "Should contain service info and endpoints",
                ),
            ),
            TestCase(
                name="Basic Health Check",
                endpoint="/health",
                description="Comprehensive health status check",
                validation_func=self.validate_basic_health_response,
            ),
            TestCase(
                name="Readiness Check",
                endpoint="/health/ready",
                description="Kubernetes-style readiness probe",
                expected_status=503,  # Service may not be ready, 503 is valid
                validation_func=self.validate_readiness_response,
            ),
            TestCase(
                name="Liveness Check",
                endpoint="/health/live",
                description="Kubernetes-style liveness probe",
                validation_func=self.validate_liveness_response,
                timeout=2.0,  # Should be very fast
            ),
            TestCase(
                name="Health Trends",
                endpoint="/health/trends",
                description="Health trends analysis over time",
            ),
            TestCase(
                name="Performance Metrics",
                endpoint="/health/metrics",
                description="System and application metrics",
                validation_func=self.validate_metrics_response,
            ),
            TestCase(
                name="Circuit Breaker Status",
                endpoint="/health/circuit-breakers",
                description="Circuit breaker states and statistics",
            ),
            TestCase(
                name="Health History",
                endpoint="/health/history",
                description="Historical health check data",
            ),
            TestCase(
                name="Active Alerts",
                endpoint="/health/alerts",
                description="Current system alerts and warnings",
            ),
            TestCase(
                name="Manual Recovery Trigger",
                endpoint="/health/recovery",
                method="POST",
                description="Trigger manual service recovery",
                expected_status=405,  # Method Not Allowed - endpoint may not support POST
            ),
        ]

    def run_single_test(self, test_case: TestCase) -> TestReport:
        """Run a single test case and log results"""
        self.log(f"Running: {test_case.name}")

        report = self.execute_test_case(test_case)
        self.test_reports.append(report)

        # Log result
        if report.result == TestResult.PASS:
            self.log(f"✅ {test_case.name} - {report.response_time_ms:.1f}ms", "PASS")
        elif report.result == TestResult.WARN:
            self.log(f"⚠️  {test_case.name} - {report.error_message}", "WARN")
        else:
            self.log(f"❌ {test_case.name} - {report.error_message}", "ERROR")

        if self.verbose and report.response_data:
            self.log(
                f"Response preview: {json.dumps(report.response_data, indent=2)[:200]}..."
            )

        return report

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all health endpoint tests"""
        self.log("🚀 Starting Wellbot Bridge Health Endpoint Tests")
        self.log(f"Target URL: {self.base_url}")
        self.log(f"Timeout: {self.timeout}s")

        test_cases = self.get_test_cases()
        start_time = time.time()

        # Check if service is reachable first
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            self.log(f"✅ Service is reachable (status: {response.status_code})")
        except Exception as e:
            self.log(f"❌ Service unreachable: {e}", "ERROR")
            return self.generate_summary()

        # Run all tests
        for test_case in test_cases:
            self.run_single_test(test_case)
            time.sleep(0.5)  # Brief pause between tests

        total_time = time.time() - start_time
        self.log(f"🏁 All tests completed in {total_time:.1f}s")

        return self.generate_summary()

    def run_load_test(
        self,
        endpoint: str = "/health",
        concurrent_requests: int = 10,
        total_requests: int = 100,
    ) -> Dict[str, Any]:
        """Run load test on specific endpoint"""
        self.log(
            f"🔥 Starting load test: {concurrent_requests} concurrent, {total_requests} total"
        )

        url = f"{self.base_url}{endpoint}"
        results = []
        start_time = time.time()

        def make_request():
            request_start = time.time()
            try:
                response = self.session.get(url, timeout=self.timeout)
                response_time = (time.time() - request_start) * 1000
                return {
                    "status_code": response.status_code,
                    "response_time_ms": response_time,
                    "success": response.status_code == 200,
                }
            except Exception as e:
                return {
                    "status_code": None,
                    "response_time_ms": (time.time() - request_start) * 1000,
                    "success": False,
                    "error": str(e),
                }

        # Execute concurrent requests
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=concurrent_requests
        ) as executor:
            futures = [executor.submit(make_request) for _ in range(total_requests)]
            results = [
                future.result() for future in concurrent.futures.as_completed(futures)
            ]

        total_time = time.time() - start_time

        # Analyze results
        successful_requests = sum(1 for r in results if r["success"])
        failed_requests = total_requests - successful_requests
        response_times = [
            r["response_time_ms"] for r in results if "response_time_ms" in r
        ]

        avg_response_time = (
            sum(response_times) / len(response_times) if response_times else 0
        )
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0

        load_test_summary = {
            "endpoint": endpoint,
            "total_requests": total_requests,
            "concurrent_requests": concurrent_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": (successful_requests / total_requests) * 100,
            "total_time_seconds": total_time,
            "requests_per_second": total_requests / total_time,
            "avg_response_time_ms": avg_response_time,
            "min_response_time_ms": min_response_time,
            "max_response_time_ms": max_response_time,
        }

        self.log("📊 Load test results:")
        self.log(f"   Success rate: {load_test_summary['success_rate']:.1f}%")
        self.log(f"   Requests/sec: {load_test_summary['requests_per_second']:.1f}")
        self.log(f"   Avg response: {avg_response_time:.1f}ms")

        return load_test_summary

    def run_stress_test(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """Run stress test for specified duration"""
        self.log(f"💪 Starting stress test for {duration_seconds} seconds")

        end_time = time.time() + duration_seconds
        request_count = 0
        error_count = 0
        response_times = []

        while time.time() < end_time:
            start_request = time.time()
            try:
                response = self.session.get(f"{self.base_url}/health", timeout=5)
                response_time = (time.time() - start_request) * 1000
                response_times.append(response_time)

                if response.status_code != 200:
                    error_count += 1

            except Exception:
                error_count += 1

            request_count += 1
            time.sleep(0.1)  # Brief pause

        avg_response_time = (
            sum(response_times) / len(response_times) if response_times else 0
        )

        stress_results = {
            "duration_seconds": duration_seconds,
            "total_requests": request_count,
            "error_count": error_count,
            "success_rate": ((request_count - error_count) / request_count) * 100
            if request_count > 0
            else 0,
            "avg_response_time_ms": avg_response_time,
            "requests_per_second": request_count / duration_seconds,
        }

        self.log("💪 Stress test completed:")
        self.log(f"   Total requests: {request_count}")
        self.log(f"   Success rate: {stress_results['success_rate']:.1f}%")
        self.log(f"   Avg response: {avg_response_time:.1f}ms")

        return stress_results

    def generate_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        if not self.test_reports:
            return {"error": "No tests executed"}

        total_tests = len(self.test_reports)
        passed_tests = sum(1 for r in self.test_reports if r.result == TestResult.PASS)
        failed_tests = sum(1 for r in self.test_reports if r.result == TestResult.FAIL)
        warned_tests = sum(1 for r in self.test_reports if r.result == TestResult.WARN)

        response_times = [
            r.response_time_ms for r in self.test_reports if r.response_time_ms
        ]
        avg_response_time = (
            sum(response_times) / len(response_times) if response_times else 0
        )

        summary = {
            "test_execution": {
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "warned_tests": warned_tests,
                "success_rate": (passed_tests / total_tests) * 100
                if total_tests > 0
                else 0,
            },
            "performance": {
                "avg_response_time_ms": avg_response_time,
                "min_response_time_ms": min(response_times) if response_times else 0,
                "max_response_time_ms": max(response_times) if response_times else 0,
            },
            "test_details": [
                {
                    "name": r.test_name,
                    "endpoint": r.endpoint,
                    "result": r.result.value,
                    "status_code": r.status_code,
                    "response_time_ms": r.response_time_ms,
                    "error_message": r.error_message,
                }
                for r in self.test_reports
            ],
        }

        # Print summary
        self.log("📋 Test Summary:")
        self.log(
            f"   Total: {total_tests}, Passed: {passed_tests}, Failed: {failed_tests}, Warned: {warned_tests}"
        )
        self.log(f"   Success Rate: {summary['test_execution']['success_rate']:.1f}%")
        self.log(f"   Avg Response Time: {avg_response_time:.1f}ms")

        return summary

    def save_report(self, filename: Optional[str] = None) -> str:
        """Save detailed test report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"health_test_report_{timestamp}.json"

        summary = self.generate_summary()

        with open(filename, "w") as f:
            json.dump(summary, f, indent=2, default=str)

        self.log(f"📄 Report saved to: {filename}")
        return filename


def main():
    """Main test execution function"""
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge Health Endpoint Tester"
    )
    parser.add_argument(
        "--base-url",
        default="http://localhost:3030",
        help="Base URL of the service (default: http://localhost:3030)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=10.0,
        help="Request timeout in seconds (default: 10.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--load-test", action="store_true", help="Run load test after basic tests"
    )
    parser.add_argument(
        "--stress-test",
        type=int,
        metavar="SECONDS",
        help="Run stress test for specified seconds",
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=10,
        help="Concurrent requests for load test (default: 10)",
    )
    parser.add_argument(
        "--requests",
        type=int,
        default=100,
        help="Total requests for load test (default: 100)",
    )

    args = parser.parse_args()

    # Initialize tester
    tester = HealthEndpointTester(
        base_url=args.base_url, timeout=args.timeout, verbose=args.verbose
    )

    try:
        # Run basic health tests
        summary = tester.run_all_tests()

        # Run load test if requested
        if args.load_test:
            load_results = tester.run_load_test(
                concurrent_requests=args.concurrent, total_requests=args.requests
            )
            summary["load_test"] = load_results

        # Run stress test if requested
        if args.stress_test:
            stress_results = tester.run_stress_test(duration_seconds=args.stress_test)
            summary["stress_test"] = stress_results

        # Save report if requested
        if args.save_report:
            tester.save_report()

        # Exit with appropriate code
        failed_tests = summary["test_execution"]["failed_tests"]
        sys.exit(1 if failed_tests > 0 else 0)

    except KeyboardInterrupt:
        tester.log("🛑 Tests interrupted by user", "WARN")
        sys.exit(130)
    except Exception as e:
        tester.log(f"💥 Unexpected error: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
