/*!
# Bridge Service

Comprehensive bridge service that orchestrates WhatsApp messaging with AI processing.
Handles WebSocket communication, JID authorization, message routing, and AI integration.
*/

use std::{sync::Arc, time::Duration};

use tokio::{
    sync::{Mutex, RwLock, broadcast},
    task::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    time::timeout,
};
use tracing::{debug, error, info, instrument, warn};

use crate::{
    config::Config,
    error::{BridgeError, BridgeResult},
    services::{
        ai_client::AiClient, chat_port_client::ChatPortClient, health_checker::HealthChecker,
        jid_authorization::JidAuthorizationService, message_processor::MessageProcessor,
    },
    types::{ExplanationRequest, IncomingWhatsAppData, SendMessageRequest},
    utils::get_env,
};

/// Bridge service statistics
#[derive(Debug, Clone, Default)]
pub struct BridgeStats {
    pub messages_processed: u64,
    pub messages_authorized: u64,
    pub messages_unauthorized: u64,
    pub ai_requests_sent: u64,
    pub ai_responses_received: u64,
    pub websocket_reconnections: u64,
    pub errors_encountered: u64,
}

/// Bridge service configuration for runtime behavior
#[derive(Debug, Clone)]
pub struct BridgeServiceConfig {
    pub allowed_jids: Vec<String>,
    pub enable_background_processing: bool,
    pub websocket_auto_reconnect: bool,
    pub ai_processing_enabled: bool,
    pub max_concurrent_ai_requests: usize,
}

impl BridgeServiceConfig {
    /// Create a new configuration from environment variables.
    pub fn from_env() -> BridgeResult<Self> {
        Ok(Self {
            allowed_jids: std::env::var("ALLOWED_JIDS")
                .map(|s| s.split(',').map(String::from).collect())
                .unwrap_or_else(|_| vec![]),
            enable_background_processing: get_env("ENABLE_BACKGROUND_PROCESSING", true),
            websocket_auto_reconnect: get_env("WEBSOCKET_AUTO_RECONNECT", true),
            ai_processing_enabled: get_env("AI_PROCESSING_ENABLED", true),
            max_concurrent_ai_requests: get_env("MAX_CONCURRENT_AI_REQUESTS", 5),
        })
    }
}

impl Default for BridgeServiceConfig {
    fn default() -> Self {
        Self {
            allowed_jids: vec![],
            enable_background_processing: true,
            websocket_auto_reconnect: true,
            ai_processing_enabled: true,
            max_concurrent_ai_requests: 5,
        }
    }
}

/// Main bridge service that orchestrates all components
#[derive(Debug)]
pub struct BridgeService {
    config: Config,
    runtime_config: Arc<RwLock<BridgeServiceConfig>>,
    ai_client: Arc<AiClient>,
    chat_port_client: Arc<ChatPortClient>,
    message_processor: Arc<MessageProcessor>,
    health_checker: Arc<HealthChecker>,
    jid_authorization: Arc<JidAuthorizationService>,
    stats: Arc<Mutex<BridgeStats>>,

    // Communication channels
    message_tx: broadcast::Sender<IncomingWhatsAppData>,
    shutdown_tx: broadcast::Sender<()>,

    // Background task handles
    processing_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
    health_monitoring_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
    recovery_monitoring_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
}

impl BridgeService {
    /// Create a new bridge service with all components
    #[instrument(skip(config))]
    pub async fn new(
        config: Config,
        jid_authorization: Arc<JidAuthorizationService>,
    ) -> BridgeResult<Self> {
        Self::new_with_runtime_config(config, BridgeServiceConfig::default(), jid_authorization)
            .await
    }

    /// Create a new bridge service with custom runtime configuration
    #[instrument(skip(config, runtime_config))]
    pub async fn new_with_runtime_config(
        config: Config,
        runtime_config: BridgeServiceConfig,
        jid_authorization: Arc<JidAuthorizationService>,
    ) -> BridgeResult<Self> {
        info!("🚀 Initializing Wellbot Bridge Service components...");

        // Initialize AI client
        let ai_client = Arc::new(AiClient::new(config.ai_service.clone())?);
        info!("✅ AI client initialized");

        // Initialize chat-port client
        let chat_port_client = Arc::new(ChatPortClient::new(config.chat_port.clone())?);
        info!("✅ Chat-port client initialized");

        // Initialize message processor with AI client integration
        let message_processor = Arc::new(
            MessageProcessor::new_with_ai_client(
                config.bridge.clone(),
                chat_port_client.clone(),
                Some(ai_client.clone()),
                jid_authorization.clone(),
            )
            .await,
        );
        info!("✅ Message processor initialized with AI integration");

        // Initialize health checker
        let mut health_checker = HealthChecker::new(
            config.health.clone(),
            ai_client.clone(),
            chat_port_client.clone(),
        );
        health_checker.set_message_processor(message_processor.clone());
        let health_checker = Arc::new(health_checker);
        info!("✅ Health checker initialized with comprehensive monitoring");

        // Create communication channels
        let (message_tx, _) = broadcast::channel(1000);
        let (shutdown_tx, _) = broadcast::channel(10);

        let service = Self {
            config,
            runtime_config: Arc::new(RwLock::new(runtime_config)),
            ai_client,
            chat_port_client,
            message_processor,
            health_checker,
            jid_authorization,
            stats: Arc::new(Mutex::new(BridgeStats::default())),
            message_tx,
            shutdown_tx,
            processing_handle: Arc::new(Mutex::new(None)),
            health_monitoring_handle: Arc::new(Mutex::new(None)),
            recovery_monitoring_handle: Arc::new(Mutex::new(None)),
        };

        info!("🎉 Bridge service initialized successfully");
        Ok(service)
    }

    /// Start the bridge service with background processing
    #[instrument(skip(self))]
    pub async fn start(&self) -> BridgeResult<()> {
        info!("🚀 Starting Wellbot Bridge Service...");

        let runtime_config = self.runtime_config.read().await;

        if runtime_config.enable_background_processing {
            self.start_background_tasks().await?;
            info!("✅ Background processing enabled");
        }

        if runtime_config.websocket_auto_reconnect {
            self.chat_port_client.start_websocket().await?;
            info!("✅ WebSocket connection started");
        }

        // Start health monitoring
        self.start_health_monitoring().await?;
        info!("✅ Health monitoring started");

        // Start recovery monitoring
        self.start_recovery_monitoring().await?;
        info!("✅ Recovery monitoring started");

        // Start metrics collection
        self.health_checker.start_metrics_collection().await?;
        info!("✅ Metrics collection started");

        info!("🎉 Bridge service started successfully with comprehensive monitoring");
        Ok(())
    }

    /// Stop the bridge service gracefully
    #[instrument(skip(self))]
    pub async fn stop(&self) -> BridgeResult<()> {
        info!("🛑 Stopping Wellbot Bridge Service...");

        // Send shutdown signal
        match self.shutdown_tx.send(()) {
            Ok(_) => debug!("🔔 Shutdown signal sent successfully"),
            Err(_) => debug!("📭 No active shutdown listeners (this is normal)"),
        }

        // Stop background tasks
        self.stop_background_tasks().await;

        // Stop health monitoring
        self.stop_health_monitoring().await;

        // Stop recovery monitoring
        self.stop_recovery_monitoring().await;

        // Stop WebSocket connection
        if let Err(e) = self.chat_port_client.stop_websocket().await {
            warn!("Failed to stop WebSocket connection: {}", e);
        }

        info!("✅ Bridge service stopped gracefully");
        Ok(())
    }

    /// Get current service statistics
    pub async fn get_stats(&self) -> BridgeStats {
        self.stats.lock().await.clone()
    }

    /// Update allowed JIDs list
    #[instrument(skip(self, jids))]
    pub async fn update_allowed_jids(&self, jids: Vec<String>) -> BridgeResult<()> {
        info!("📝 Updating allowed JIDs list with {} entries", jids.len());

        for jid in jids {
            self.jid_authorization.add_jid(jid, None).await?;
        }

        info!("✅ Allowed JIDs updated successfully");
        Ok(())
    }

    /// Add a single JID to the allowed list
    #[instrument(skip(self))]
    pub async fn add_allowed_jid(&self, jid: String) -> BridgeResult<()> {
        info!("➕ Adding JID to allowed list: {}", jid);
        self.jid_authorization.add_jid(jid, None).await?;
        info!("✅ JID added successfully");
        Ok(())
    }

    /// Remove a JID from the allowed list
    #[instrument(skip(self))]
    pub async fn remove_allowed_jid(&self, jid: &str) -> BridgeResult<()> {
        info!("➖ Removing JID from allowed list: {}", jid);
        self.jid_authorization.remove_jid(jid).await?;
        info!("✅ JID removed successfully");
        Ok(())
    }

    /// Get current allowed JIDs
    pub async fn get_allowed_jids(&self) -> Vec<String> {
        self.jid_authorization.get_authorized_jids().await
    }

    /// Process a single WhatsApp message with AI integration
    #[instrument(skip(self, message))]
    pub async fn process_whatsapp_message(
        &self,
        message: IncomingWhatsAppData,
    ) -> BridgeResult<()> {
        debug!("📨 Processing WhatsApp message from: {}", message.from);

        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_processed += 1;
        }

        // Check if JID is authorized
        if !self.jid_authorization.is_authorized(&message.from).await {
            warn!("🚫 Unauthorized message from JID: {}", message.from);

            let mut stats = self.stats.lock().await;
            stats.messages_unauthorized += 1;

            return Err(Box::new(BridgeError::Authentication(format!(
                "JID not authorized: {}",
                message.from
            ))));
        }

        // Update authorized stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_authorized += 1;
        }

        // Process with AI if enabled
        if self.runtime_config.read().await.ai_processing_enabled {
            self.process_with_ai(message).await?;
        } else {
            // Use message processor for basic processing
            self.message_processor.process_message(message).await?;
        }

        Ok(())
    }

    /// Process message with AI integration and circuit breaker logic
    #[instrument(skip(self, message))]
    async fn process_with_ai(&self, message: IncomingWhatsAppData) -> BridgeResult<()> {
        debug!("🤖 Processing message with AI: {}", message.message_id);

        // Check if AI service should be bypassed due to circuit breaker
        if self.should_bypass_component("ai_service").await {
            warn!("🚫 AI service bypassed due to circuit breaker - sending fallback response");

            let fallback_message = "I'm currently experiencing technical difficulties. Please try again in a few moments.";
            let send_request = SendMessageRequest {
                number: message.from.clone(),
                message: fallback_message.to_string(),
            };

            self.chat_port_client.send_message(send_request).await?;
            return Ok(());
        }

        // Update AI request stats
        {
            let mut stats = self.stats.lock().await;
            stats.ai_requests_sent += 1;
        }

        // Create AI request
        let ai_request = ExplanationRequest {
            prompt: format!(
                "WhatsApp message from {}: {}",
                message.from, message.message
            ),
            temperature: None,
            max_tokens: None,
        };

        // Send to AI service with timeout
        let ai_timeout = Duration::from_secs(self.config.ai_service.timeout_secs);
        let start_time = std::time::Instant::now();
        let ai_response =
            timeout(ai_timeout, self.ai_client.generate_explanation(ai_request)).await;

        let response_time_ms = start_time.elapsed().as_millis() as u64;

        match ai_response {
            Ok(Ok(response)) => {
                debug!(
                    "✅ AI response received for message: {} ({}ms)",
                    message.message_id, response_time_ms
                );

                // Update AI response stats
                {
                    let mut stats = self.stats.lock().await;
                    stats.ai_responses_received += 1;
                }

                // Update component metrics
                self.health_checker
                    .update_component_metrics("ai_service", response_time_ms, true)
                    .await;

                // Send response back via WhatsApp
                let send_request = SendMessageRequest {
                    number: message.from.clone(),
                    message: response.content,
                };

                if let Err(e) = self.chat_port_client.send_message(send_request).await {
                    error!("Failed to send AI response via WhatsApp: {}", e);

                    let mut stats = self.stats.lock().await;
                    stats.errors_encountered += 1;

                    return Err(e);
                }

                info!("📤 AI response sent successfully to: {}", message.from);
            }
            Ok(Err(e)) => {
                error!("AI service error for message {}: {}", message.message_id, e);

                let mut stats = self.stats.lock().await;
                stats.errors_encountered += 1;

                // Update component metrics for failure
                self.health_checker
                    .update_component_metrics("ai_service", response_time_ms, false)
                    .await;

                // Send error response to user
                self.send_error_response(
                    &message,
                    "I'm having trouble processing your request right now. Please try again later.",
                )
                .await?;

                return Err(e);
            }
            Err(_) => {
                error!("AI request timeout for message: {}", message.message_id);

                let mut stats = self.stats.lock().await;
                stats.errors_encountered += 1;

                // Update component metrics for timeout
                self.health_checker
                    .update_component_metrics("ai_service", response_time_ms, false)
                    .await;

                // Send timeout response to user
                self.send_error_response(
                    &message,
                    "Your request is taking longer than expected. Please try again.",
                )
                .await?;

                return Err(Box::new(BridgeError::Timeout(
                    "AI request timeout".to_string(),
                )));
            }
        }

        Ok(())
    }

    /// Send error response to WhatsApp user
    #[instrument(skip(self, message, error_text))]
    async fn send_error_response(
        &self,
        message: &IncomingWhatsAppData,
        error_text: &str,
    ) -> BridgeResult<()> {
        let send_request = SendMessageRequest {
            number: message.from.clone(),
            message: error_text.to_string(),
        };

        self.chat_port_client.send_message(send_request).await?;
        debug!("📤 Error response sent to: {}", message.from);
        Ok(())
    }

    /// Start background processing tasks
    #[instrument(skip(self))]
    async fn start_background_tasks(&self) -> BridgeResult<()> {
        info!("🔄 Starting background processing tasks...");

        // Start message processing task
        let processing_handle = self.spawn_message_processing_task().await;
        *self.processing_handle.lock().await = Some(processing_handle);

        // Start WebSocket message listener task
        let _websocket_handle = self.spawn_websocket_listener_task().await?;
        // Note: We could store this handle if we need to manage it separately

        info!("✅ Background tasks started");
        Ok(())
    }

    /// Stop background tasks
    async fn stop_background_tasks(&self) {
        info!("🛑 Stopping background tasks...");

        // Stop processing task
        if let Some(handle) = self.processing_handle.lock().await.take() {
            handle.abort();
            debug!("✅ Processing task stopped");
        }

        info!("✅ All background tasks stopped");
    }

    /// Spawn message processing background task
    async fn spawn_message_processing_task(&self) -> JoinHandle<()> {
        let mut message_rx = self.message_tx.subscribe();
        let mut shutdown_rx = self.shutdown_tx.subscribe();

        // Clone the necessary components for the background task
        let ai_client = self.ai_client.clone();
        let chat_port_client = self.chat_port_client.clone();
        let jid_authorization = self.jid_authorization.clone();
        let health_checker = self.health_checker.clone();
        let stats = self.stats.clone();
        let runtime_config = self.runtime_config.clone();

        tokio::spawn(async move {
            info!("🔄 Message processing task started");

            loop {
                tokio::select! {
                    message_result = message_rx.recv() => {
                        match message_result {
                            Ok(message) => {
                                debug!("📨 Background processing message: {}", message.message_id);

                                // Process message directly using cloned components
                                if let Err(e) = Self::process_message_with_components(
                                    message,
                                    &ai_client,
                                    &chat_port_client,
                                    &jid_authorization,
                                    &health_checker,
                                    &stats,
                                    &runtime_config,
                                ).await {
                                    error!("Background message processing error: {}", e);
                                }
                            }
                            Err(e) => {
                                error!("Message channel error: {}", e);
                                break;
                            }
                        }
                    }
                    _ = shutdown_rx.recv() => {
                        info!("🛑 Message processing task received shutdown signal");
                        break;
                    }
                }
            }

            info!("✅ Message processing task stopped");
        })
    }

    /// Spawn WebSocket listener task to automatically process incoming WhatsApp messages
    #[instrument(skip(self))]
    async fn spawn_websocket_listener_task(&self) -> BridgeResult<JoinHandle<()>> {
        let chat_port_client = self.chat_port_client.clone();
        let message_tx = self.message_tx.clone();
        let mut shutdown_rx = self.shutdown_tx.subscribe();

        info!("🔌 Starting WebSocket listener task...");

        let handle = tokio::spawn(async move {
            info!("🔄 WebSocket listener task started");

            // Create message handler that forwards WhatsApp messages to processing queue
            let message_handler = move |message: crate::types::WebSocketMessage| {
                let message_tx = message_tx.clone();
                async move {
                    match message {
                        crate::types::WebSocketMessage::IncomingWhatsApp { data, .. } => {
                            info!(
                                "📱 Received WhatsApp message from {}: {}",
                                data.from,
                                data.message.chars().take(50).collect::<String>()
                            );

                            // Forward message to processing queue
                            if let Err(e) = message_tx.send(data) {
                                error!(
                                    "Failed to forward WhatsApp message to processing queue: {}",
                                    e
                                );
                            } else {
                                debug!("✅ WhatsApp message forwarded to processing queue");
                            }
                        }
                        crate::types::WebSocketMessage::StatusUpdate { data, .. } => {
                            debug!("📊 Status update: {} is {}", data.service, data.status);
                        }
                        crate::types::WebSocketMessage::Error { error, .. } => {
                            warn!("❌ WebSocket error received: {}", error);
                        }
                        _ => {
                            debug!("📨 Received other WebSocket message type");
                        }
                    }
                }
            };

            // Start listening for WebSocket messages
            tokio::select! {
                result = chat_port_client.listen_for_messages(message_handler) => {
                    match result {
                        Ok(()) => {
                            info!("✅ WebSocket listener completed normally");
                        }
                        Err(e) => {
                            error!("❌ WebSocket listener error: {}", e);
                        }
                    }
                }
                _ = shutdown_rx.recv() => {
                    info!("🛑 WebSocket listener task received shutdown signal");
                }
            }

            info!("✅ WebSocket listener task stopped");
        });

        Ok(handle)
    }

    /// Process a WhatsApp message using provided components (for background tasks)
    async fn process_message_with_components(
        message: IncomingWhatsAppData,
        ai_client: &Arc<AiClient>,
        chat_port_client: &Arc<ChatPortClient>,
        jid_authorization: &Arc<JidAuthorizationService>,
        health_checker: &Arc<HealthChecker>,
        stats: &Arc<Mutex<BridgeStats>>,
        runtime_config: &Arc<RwLock<BridgeServiceConfig>>,
    ) -> BridgeResult<()> {
        debug!("📨 Processing WhatsApp message from: {}", message.from);

        // Update stats
        {
            let mut stats = stats.lock().await;
            stats.messages_processed += 1;
        }

        // Check if JID is authorized
        if !jid_authorization.is_authorized(&message.from).await {
            warn!("🚫 Unauthorized message from JID: {}", message.from);

            let mut stats = stats.lock().await;
            stats.messages_unauthorized += 1;

            return Err(Box::new(BridgeError::Authentication(format!(
                "JID not authorized: {}",
                message.from
            ))));
        }

        // Update authorized stats
        {
            let mut stats = stats.lock().await;
            stats.messages_authorized += 1;
        }

        // Process with AI if enabled
        let config = runtime_config.read().await;
        if config.ai_processing_enabled {
            Self::process_with_ai_components(
                message,
                ai_client,
                chat_port_client,
                health_checker,
                stats,
            )
            .await?;
        } else {
            // Send a basic acknowledgment response
            let send_request = SendMessageRequest {
                number: message.from.clone(),
                message: "Message received and processed.".to_string(),
            };

            chat_port_client.send_message(send_request).await?;
        }

        Ok(())
    }

    /// Process message with AI integration using provided components
    async fn process_with_ai_components(
        message: IncomingWhatsAppData,
        ai_client: &Arc<AiClient>,
        chat_port_client: &Arc<ChatPortClient>,
        health_checker: &Arc<HealthChecker>,
        stats: &Arc<Mutex<BridgeStats>>,
    ) -> BridgeResult<()> {
        debug!("🤖 Processing message with AI: {}", message.message_id);

        // Update AI request stats
        {
            let mut stats = stats.lock().await;
            stats.ai_requests_sent += 1;
        }

        // Create AI request
        let ai_request = ExplanationRequest {
            prompt: format!(
                "WhatsApp message from {}: {}",
                message.from, message.message
            ),
            temperature: None,
            max_tokens: None,
        };

        // Send to AI service with timeout
        let ai_timeout = Duration::from_secs(30); // Default timeout
        let start_time = std::time::Instant::now();
        let ai_response = timeout(ai_timeout, ai_client.generate_explanation(ai_request)).await;

        let response_time_ms = start_time.elapsed().as_millis() as u64;

        match ai_response {
            Ok(Ok(response)) => {
                debug!(
                    "✅ AI response received for message: {} ({}ms)",
                    message.message_id, response_time_ms
                );

                // Update AI response stats
                {
                    let mut stats = stats.lock().await;
                    stats.ai_responses_received += 1;
                }

                // Update component metrics
                health_checker
                    .update_component_metrics("ai_service", response_time_ms, true)
                    .await;

                // Send response back via WhatsApp
                let send_request = SendMessageRequest {
                    number: message.from.clone(),
                    message: response.content,
                };

                if let Err(e) = chat_port_client.send_message(send_request).await {
                    error!("Failed to send AI response via WhatsApp: {}", e);

                    let mut stats = stats.lock().await;
                    stats.errors_encountered += 1;

                    return Err(e);
                }

                info!("✅ AI response sent successfully to: {}", message.from);
            }
            Ok(Err(e)) => {
                error!("AI service error for message {}: {}", message.message_id, e);

                // Update component metrics
                health_checker
                    .update_component_metrics("ai_service", response_time_ms, false)
                    .await;

                // Send error response to user
                let error_message = "I'm sorry, I'm having trouble processing your request right now. Please try again later.";
                let send_request = SendMessageRequest {
                    number: message.from.clone(),
                    message: error_message.to_string(),
                };

                chat_port_client.send_message(send_request).await?;

                let mut stats = stats.lock().await;
                stats.errors_encountered += 1;
            }
            Err(_) => {
                error!("AI service timeout for message: {}", message.message_id);

                // Update component metrics
                health_checker
                    .update_component_metrics("ai_service", response_time_ms, false)
                    .await;

                // Send timeout response to user
                let timeout_message =
                    "I'm taking longer than usual to respond. Please try again in a moment.";
                let send_request = SendMessageRequest {
                    number: message.from.clone(),
                    message: timeout_message.to_string(),
                };

                chat_port_client.send_message(send_request).await?;

                let mut stats = stats.lock().await;
                stats.errors_encountered += 1;
            }
        }

        Ok(())
    }

    /// Health check for the bridge service using enhanced health checker
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> BridgeResult<bool> {
        debug!("🏥 Performing comprehensive bridge service health check");

        let health = self.health_checker.perform_health_check().await?;
        let is_healthy = health.status.is_operational();

        if is_healthy {
            info!(
                "✅ Bridge service health check passed - Status: {}",
                health.status
            );
        } else {
            warn!(
                "❌ Bridge service health check failed - Status: {}",
                health.status
            );
        }

        Ok(is_healthy)
    }

    /// Get comprehensive health status
    pub async fn get_health_status(
        &self,
    ) -> BridgeResult<crate::services::health_checker::ServiceHealth> {
        self.health_checker.perform_health_check().await
    }

    /// Get health checker instance
    pub fn health_checker(&self) -> &Arc<HealthChecker> {
        &self.health_checker
    }

    /// Start health monitoring background task
    async fn start_health_monitoring(&self) -> BridgeResult<()> {
        let health_checker = self.health_checker.clone();
        let handle = tokio::spawn(async move {
            if let Err(e) = health_checker.start_periodic_checks().await {
                error!("Health monitoring task failed: {}", e);
            }
        });

        *self.health_monitoring_handle.lock().await = Some(handle);
        Ok(())
    }

    /// Start recovery monitoring background task
    async fn start_recovery_monitoring(&self) -> BridgeResult<()> {
        let health_checker = self.health_checker.clone();
        let handle = tokio::spawn(async move {
            if let Err(e) = health_checker.start_recovery_monitoring().await {
                error!("Recovery monitoring task failed: {}", e);
            }
        });

        *self.recovery_monitoring_handle.lock().await = Some(handle);
        Ok(())
    }

    /// Stop health monitoring
    async fn stop_health_monitoring(&self) {
        if let Some(handle) = self.health_monitoring_handle.lock().await.take() {
            handle.abort();
            debug!("✅ Health monitoring stopped");
        }
    }

    /// Stop recovery monitoring
    async fn stop_recovery_monitoring(&self) {
        if let Some(handle) = self.recovery_monitoring_handle.lock().await.take() {
            handle.abort();
            debug!("✅ Recovery monitoring stopped");
        }
    }

    /// Check if component should be bypassed due to circuit breaker
    pub async fn should_bypass_component(&self, component_name: &str) -> bool {
        self.health_checker
            .should_bypass_component(component_name)
            .await
    }

    /// Attempt manual recovery of failed components
    pub async fn attempt_recovery(
        &self,
    ) -> BridgeResult<crate::services::health_checker::RecoveryReport> {
        self.health_checker.attempt_recovery().await
    }

    /// Get circuit breaker status for all components
    pub async fn get_circuit_breaker_status(
        &self,
    ) -> std::collections::HashMap<String, crate::services::health_checker::CircuitBreakerStatus>
    {
        self.health_checker.get_circuit_breaker_status().await
    }

    /// Get health trends analysis
    pub async fn get_health_trends(&self) -> crate::services::health_checker::HealthTrends {
        self.health_checker.get_health_trends().await
    }

    /// Get performance metrics
    pub async fn get_performance_metrics(
        &self,
    ) -> crate::services::health_checker::PerformanceMetrics {
        self.health_checker.get_performance_metrics().await
    }
}

// Implement Clone for BridgeService to enable Arc sharing
impl Clone for BridgeService {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            runtime_config: self.runtime_config.clone(),
            ai_client: self.ai_client.clone(),
            chat_port_client: self.chat_port_client.clone(),
            message_processor: self.message_processor.clone(),
            health_checker: self.health_checker.clone(),
            stats: self.stats.clone(),
            message_tx: self.message_tx.clone(),
            shutdown_tx: self.shutdown_tx.clone(),
            processing_handle: self.processing_handle.clone(),
            health_monitoring_handle: self.health_monitoring_handle.clone(),
            recovery_monitoring_handle: self.recovery_monitoring_handle.clone(),
            jid_authorization: self.jid_authorization.clone(),
        }
    }
}
