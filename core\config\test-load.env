# Load Testing Configuration
# This configuration is optimized for high-load testing scenarios

# Core Service Configuration
HEALTH_PORT=8081
AI_SERVICE_BASE_URL=http://localhost:8080
CHAT_PORT_BASE_URL=ws://localhost:3000

# Optimized Logging for Load Testing
LOG_LEVEL=warn
LOG_FORMAT=compact
LOG_FILE=logs/wellbot-bridge-load.log

# Reduced Health Check Frequency for Load Testing
HEALTH_CHECK_INTERVAL_SECS=30
HEALTH_HISTORY_RETENTION_HOURS=2
HEALTH_HISTORY_MAX_RECORDS=500

# Robust Circuit Breaker Settings for Load Testing
CIRCUIT_BREAKER_FAILURE_THRESHOLD=10
CIRCUIT_BREAKER_RECOVERY_TIMEOUT_SECS=60
CIRCUIT_BREAKER_SUCCESS_THRESHOLD=5
CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=10

# Rate-Limited Alerting for Load Testing
ALERT_MIN_SEVERITY=error
ALERT_RATE_LIMIT_WINDOW_SECS=300
ALERT_RATE_LIMIT_MAX_ALERTS=3
ALERT_WEBHOOK_URL=http://localhost:9000/webhook
ALERT_EMAIL_ENABLED=false

# Optimized Metrics Collection
METRICS_COLLECTION_INTERVAL_SECS=30
METRICS_HISTORY_RETENTION_HOURS=4
METRICS_MAX_HISTORY_RECORDS=1000

# Relaxed Health Thresholds for Load Testing
HEALTH_RESPONSE_TIME_WARNING_MS=3000
HEALTH_RESPONSE_TIME_CRITICAL_MS=8000
HEALTH_MEMORY_WARNING_PERCENT=85
HEALTH_MEMORY_CRITICAL_PERCENT=95
HEALTH_CPU_WARNING_PERCENT=80
HEALTH_CPU_CRITICAL_PERCENT=90
HEALTH_DISK_WARNING_PERCENT=80
HEALTH_DISK_CRITICAL_PERCENT=90
HEALTH_QUEUE_WARNING_PERCENT=80
HEALTH_QUEUE_CRITICAL_PERCENT=95

# AI Service Configuration for Load Testing
AI_SERVICE_TIMEOUT_SECS=15
AI_SERVICE_MAX_RETRIES=2
AI_SERVICE_RETRY_DELAY_MS=1000

# Chat Port Configuration for Load Testing
CHAT_PORT_TIMEOUT_SECS=10
CHAT_PORT_MAX_RETRIES=2
CHAT_PORT_RETRY_DELAY_MS=500
CHAT_PORT_HEARTBEAT_INTERVAL_SECS=60

# WebSocket Configuration for Load Testing
WEBSOCKET_PING_INTERVAL_SECS=60
WEBSOCKET_PONG_TIMEOUT_SECS=15
WEBSOCKET_RECONNECT_DELAY_SECS=10
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=5

# Load Testing Features
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_METRICS_EXPORT=true
ENABLE_HEALTH_HISTORY_EXPORT=false
ENABLE_CIRCUIT_BREAKER_MANUAL_CONTROL=false

# Security Settings for Load Testing
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

# Performance Tuning for Load Testing
WORKER_THREADS=8
MAX_CONCURRENT_REQUESTS=500
REQUEST_TIMEOUT_SECS=60
KEEP_ALIVE_TIMEOUT_SECS=120
CONNECTION_POOL_SIZE=100
CONNECTION_POOL_TIMEOUT_SECS=30

# Memory Management for Load Testing
MEMORY_LIMIT_MB=2048
GC_THRESHOLD_MB=1024
BUFFER_SIZE_KB=64

# Load Testing Specific Settings
LOAD_TEST_MODE=true
DISABLE_DETAILED_LOGGING=true
BATCH_METRICS_UPDATES=true
ASYNC_HEALTH_CHECKS=true
