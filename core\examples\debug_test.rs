/*!
# 🔍 Debug Test - Step by Step Service Initialization

This test helps identify where the service initialization is hanging
by testing each component individually with detailed logging.
*/

use std::{sync::Arc, time::Duration};
use tokio::time::timeout;
use wellbot_bridge::{
    config::Config,
    services::{
        ai_client::AiClient,
        bridge_service::BridgeService,
        chat_port_client::ChatPortClient,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    println!("🔍 Starting Debug Test");
    println!("======================");

    // Step 1: Test configuration loading
    println!("\n⚙️ Step 1: Testing configuration loading...");
    let config = Config::default();
    println!("✅ Configuration loaded successfully");
    println!("   AI Service URL: {}", config.ai_service.base_url);
    println!("   Chat Port WebSocket: {}", config.chat_port.websocket_url);
    println!("   Chat Port API: {}", config.chat_port.api_base_url);

    // Step 2: Test AI client initialization
    println!("\n🤖 Step 2: Testing AI client initialization...");
    match timeout(Duration::from_secs(10), async {
        AiClient::new(config.ai_service.clone())
    })
    .await
    {
        Ok(Ok(ai_client)) => {
            println!("✅ AI client initialized successfully");

            // Test AI health check
            println!("   Testing AI health check...");
            match timeout(Duration::from_secs(5), ai_client.health_check()).await {
                Ok(Ok(is_healthy)) => {
                    println!(
                        "   ✅ AI health check: {}",
                        if is_healthy { "Healthy" } else { "Unhealthy" }
                    );
                }
                Ok(Err(e)) => {
                    println!("   ⚠️ AI health check failed: {}", e);
                }
                Err(_) => {
                    println!("   ⚠️ AI health check timed out");
                }
            }
        }
        Ok(Err(e)) => {
            println!("❌ AI client initialization failed: {}", e);
        }
        Err(_) => {
            println!("❌ AI client initialization timed out");
        }
    }

    // Step 3: Test chat port client initialization
    println!("\n💬 Step 3: Testing chat port client initialization...");
    match timeout(Duration::from_secs(10), async {
        ChatPortClient::new(config.chat_port.clone())
    })
    .await
    {
        Ok(Ok(chat_client)) => {
            println!("✅ Chat port client initialized successfully");

            // Test chat port health check
            println!("   Testing chat port health check...");
            match timeout(Duration::from_secs(5), chat_client.health_check()).await {
                Ok(Ok(is_healthy)) => {
                    println!(
                        "   ✅ Chat port health check: {}",
                        if is_healthy { "Healthy" } else { "Unhealthy" }
                    );
                }
                Ok(Err(e)) => {
                    println!("   ⚠️ Chat port health check failed: {}", e);
                }
                Err(_) => {
                    println!("   ⚠️ Chat port health check timed out");
                }
            }
        }
        Ok(Err(e)) => {
            println!("❌ Chat port client initialization failed: {}", e);
        }
        Err(_) => {
            println!("❌ Chat port client initialization timed out");
        }
    }

    // Step 4: Test JID authorization service initialization
    println!("\n🔐 Step 4: Testing JID authorization service initialization...");
    match timeout(Duration::from_secs(10), async {
        let jid_config = JidAuthConfig::default();
        JidAuthorizationService::new(jid_config).await
    })
    .await
    {
        Ok(Ok(jid_service)) => {
            println!("✅ JID authorization service initialized successfully");

            // Test adding a JID
            println!("   Testing JID operations...");
            let test_jid = "<EMAIL>";
            match jid_service
                .add_jid(test_jid.to_string(), Some("Test User".to_string()))
                .await
            {
                Ok(_) => {
                    println!("   ✅ JID added successfully");

                    // Test authorization check
                    let is_authorized = jid_service.is_authorized(test_jid).await;
                    println!("   ✅ JID authorization check: {}", is_authorized);
                }
                Err(e) => {
                    println!("   ⚠️ JID add failed: {}", e);
                }
            }
        }
        Ok(Err(e)) => {
            println!("❌ JID authorization service initialization failed: {}", e);
        }
        Err(_) => {
            println!("❌ JID authorization service initialization timed out");
        }
    }

    // Step 5: Test bridge service initialization (this is where it might hang)
    println!("\n🌉 Step 5: Testing bridge service initialization...");
    println!("   This step might take longer or hang if there are issues...");

    match timeout(Duration::from_secs(30), async {
        let jid_config = JidAuthConfig::default();
        let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);
        BridgeService::new(config.clone(), jid_authorization).await
    })
    .await
    {
        Ok(Ok(bridge_service)) => {
            println!("✅ Bridge service initialized successfully");

            // Test bridge service health check
            println!("   Testing bridge service health check...");
            match timeout(Duration::from_secs(10), bridge_service.health_check()).await {
                Ok(Ok(is_healthy)) => {
                    println!("   ✅ Bridge service health check: {}", is_healthy);
                }
                Ok(Err(e)) => {
                    println!("   ⚠️ Bridge service health check failed: {}", e);
                }
                Err(_) => {
                    println!("   ⚠️ Bridge service health check timed out");
                }
            }
        }
        Ok(Err(e)) => {
            println!("❌ Bridge service initialization failed: {}", e);
        }
        Err(_) => {
            println!("❌ Bridge service initialization timed out (this is likely where it hangs)");
            println!("   Possible causes:");
            println!("   - WebSocket connection to chat-port is blocking");
            println!("   - AI service connection is blocking");
            println!("   - Health checker initialization is blocking");
            println!("   - Background task spawning is blocking");
        }
    }

    println!("\n🔍 Debug Test Summary");
    println!("=====================");
    println!("If the bridge service initialization timed out, the issue is likely:");
    println!("1. WebSocket connection to chat-port (ws://localhost:8081/ws)");
    println!("2. AI service connection (http://localhost:8000)");
    println!("3. Background task initialization");
    println!("\nCheck that both services are running and accessible.");

    Ok(())
}
