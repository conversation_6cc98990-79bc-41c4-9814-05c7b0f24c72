#!/usr/bin/env python3
"""
Simple Message Processing test runner for Wellbot Bridge Service

This script provides easy-to-use commands for running message processing tests
with different configurations and scenarios.

Usage Examples:
    python run_message_tests.py                    # Basic message processing tests
    python run_message_tests.py --verbose          # Verbose output
    python run_message_tests.py --save-report      # Save detailed report
    python run_message_tests.py --timeout 60       # Extended timeout for AI tests
"""

import sys
import subprocess
import argparse
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import websockets
        import requests

        print("✅ Required libraries are available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Install with: pip install websockets requests")
        return False


def check_services_status(
    bridge_url="http://localhost:3030",
    chat_port_url="ws://localhost:8081/ws",
    ai_service_url="http://localhost:8000",
):
    """Check if required services are running"""
    import requests
    import asyncio
    import websockets

    services_status = {}

    # Check Bridge Service
    try:
        response = requests.get(f"{bridge_url}/health", timeout=5)
        services_status["bridge"] = response.status_code == 200
        if services_status["bridge"]:
            print(f"✅ Bridge service is running at {bridge_url}")
        else:
            print(f"⚠️ Bridge service responded with status {response.status_code}")
    except Exception:
        services_status["bridge"] = False
        print(f"❌ Bridge service not reachable at {bridge_url}")

    # Check Chat-port Service
    async def test_chat_port():
        try:
            async with websockets.connect(chat_port_url, timeout=5.0) as websocket:
                return True
        except Exception:
            return False

    try:
        services_status["chat_port"] = asyncio.run(test_chat_port())
        if services_status["chat_port"]:
            print(f"✅ Chat-port service is running at {chat_port_url}")
        else:
            print(f"❌ Chat-port service not reachable at {chat_port_url}")
    except Exception:
        services_status["chat_port"] = False
        print(f"❌ Error checking chat-port service")

    # Check AI Service (optional)
    try:
        response = requests.get(f"{ai_service_url}/health", timeout=5)
        services_status["ai"] = response.status_code == 200
        if services_status["ai"]:
            print(f"✅ AI service is running at {ai_service_url}")
        else:
            print(f"⚠️ AI service responded with status {response.status_code}")
    except Exception:
        services_status["ai"] = False
        print(
            f"⚠️ AI service not reachable at {ai_service_url} (tests will skip AI features)"
        )

    # Check minimum requirements
    if not services_status["bridge"] or not services_status["chat_port"]:
        print("\n❌ Required services are not available:")
        if not services_status["bridge"]:
            print("  - Start Bridge service: cd core && cargo run --bin wellbot-bridge")
        if not services_status["chat_port"]:
            print("  - Start Chat-port service: cd chat-port && go run cmd/main.go")
        return False

    return True


def run_message_processing_tests(args):
    """Run message processing tests"""
    cmd = [sys.executable, "message_processing_tests.py"]

    if args.bridge_url:
        cmd.extend(["--bridge-url", args.bridge_url])
    if args.chat_port_url:
        cmd.extend(["--chat-port-url", args.chat_port_url])
    if args.ai_service_url:
        cmd.extend(["--ai-service-url", args.ai_service_url])
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")

    print(f"🚀 Running message processing tests: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge Message Processing Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_message_tests.py                    # Basic message processing tests
  python run_message_tests.py --verbose          # Verbose output
  python run_message_tests.py --save-report      # Save detailed report
  python run_message_tests.py --timeout 60       # Extended timeout for AI tests
  
  # Custom service URLs
  python run_message_tests.py --bridge-url http://localhost:3030
  python run_message_tests.py --ai-service-url http://localhost:8000
  
  # Full test with reporting
  python run_message_tests.py --verbose --save-report --timeout 60
        """,
    )

    parser.add_argument(
        "--bridge-url",
        default="http://localhost:3030",
        help="Bridge service URL (default: http://localhost:3030)",
    )
    parser.add_argument(
        "--chat-port-url",
        default="ws://localhost:8081/ws",
        help="Chat-port WebSocket URL (default: ws://localhost:8081/ws)",
    )
    parser.add_argument(
        "--ai-service-url",
        default="http://localhost:8000",
        help="AI service URL (default: http://localhost:8000)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=30.0,
        help="Request timeout in seconds (default: 30.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )

    # Additional options
    parser.add_argument(
        "--skip-checks", action="store_true", help="Skip dependency and service checks"
    )

    args = parser.parse_args()

    print("📨 Wellbot Bridge Message Processing Test Runner")
    print("=" * 55)

    # Check dependencies and service status
    if not args.skip_checks:
        if not check_dependencies():
            return 1

        if not check_services_status(
            args.bridge_url, args.chat_port_url, args.ai_service_url
        ):
            return 1

    # Run message processing tests
    try:
        result = run_message_processing_tests(args)
        return result.returncode

    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
