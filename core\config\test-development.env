# Development Configuration for Testing and Debugging
# This configuration is optimized for development with detailed logging and monitoring

# Core Service Configuration
HEALTH_PORT=8081
AI_SERVICE_BASE_URL=http://localhost:8080
CHAT_PORT_BASE_URL=ws://localhost:3000

# Enhanced Logging for Development
LOG_LEVEL=debug
LOG_FORMAT=pretty
LOG_FILE=logs/wellbot-bridge-dev.log

# Frequent Health Checks for Development
HEALTH_CHECK_INTERVAL_SECS=15
HEALTH_HISTORY_RETENTION_HOURS=6
HEALTH_HISTORY_MAX_RECORDS=1000

# Sensitive Circuit Breaker Settings for Testing
CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
CIRCUIT_BREAKER_RECOVERY_TIMEOUT_SECS=30
CIRCUIT_BREAKER_SUCCESS_THRESHOLD=2
CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=5

# Comprehensive Alerting for Development
ALERT_MIN_SEVERITY=info
ALERT_RATE_LIMIT_WINDOW_SECS=60
ALERT_RATE_LIMIT_MAX_ALERTS=10
ALERT_WEBHOOK_URL=http://localhost:9000/webhook
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_SMTP_HOST=localhost
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_FROM=wellbot-dev@localhost
ALERT_EMAIL_TO=developer@localhost

# Frequent Metrics Collection
METRICS_COLLECTION_INTERVAL_SECS=10
METRICS_HISTORY_RETENTION_HOURS=12
METRICS_MAX_HISTORY_RECORDS=5000

# Strict Health Thresholds for Development Testing
HEALTH_RESPONSE_TIME_WARNING_MS=1000
HEALTH_RESPONSE_TIME_CRITICAL_MS=3000
HEALTH_MEMORY_WARNING_PERCENT=60
HEALTH_MEMORY_CRITICAL_PERCENT=75
HEALTH_CPU_WARNING_PERCENT=50
HEALTH_CPU_CRITICAL_PERCENT=70
HEALTH_DISK_WARNING_PERCENT=70
HEALTH_DISK_CRITICAL_PERCENT=85
HEALTH_QUEUE_WARNING_PERCENT=60
HEALTH_QUEUE_CRITICAL_PERCENT=80

# AI Service Configuration
AI_SERVICE_TIMEOUT_SECS=10
AI_SERVICE_MAX_RETRIES=3
AI_SERVICE_RETRY_DELAY_MS=500

# Chat Port Configuration
CHAT_PORT_TIMEOUT_SECS=5
CHAT_PORT_MAX_RETRIES=3
CHAT_PORT_RETRY_DELAY_MS=200
CHAT_PORT_HEARTBEAT_INTERVAL_SECS=30

# WebSocket Configuration
WEBSOCKET_PING_INTERVAL_SECS=30
WEBSOCKET_PONG_TIMEOUT_SECS=10
WEBSOCKET_RECONNECT_DELAY_SECS=5
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=10

# Development Features
ENABLE_DEBUG_ENDPOINTS=true
ENABLE_METRICS_EXPORT=true
ENABLE_HEALTH_HISTORY_EXPORT=true
ENABLE_CIRCUIT_BREAKER_MANUAL_CONTROL=true

# Security Settings (Relaxed for Development)
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
RATE_LIMIT_ENABLED=false

# Performance Tuning for Development
WORKER_THREADS=4
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_SECS=30
KEEP_ALIVE_TIMEOUT_SECS=60
