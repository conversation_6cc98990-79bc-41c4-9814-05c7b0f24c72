#!/bin/bash
# monitor.sh - Continuous health monitoring for Wellbot Bridge Service
# Usage: ./monitor.sh [base_url] [interval] [duration]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8081}"
INTERVAL="${2:-30}"
DURATION="${3:-0}"  # 0 means run indefinitely
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
LOG_FILE="health_monitor_${TIMESTAMP}.log"
ALERT_LOG="alerts_${TIMESTAMP}.log"
METRICS_LOG="metrics_${TIMESTAMP}.log"

# Thresholds for alerting
RESPONSE_TIME_THRESHOLD=5.0
ERROR_RATE_THRESHOLD=5
CONSECUTIVE_FAILURES_THRESHOLD=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Monitoring state
CONSECUTIVE_FAILURES=0
TOTAL_CHECKS=0
FAILED_CHECKS=0
START_TIME=$(date +%s)

# Functions
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[INFO]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[OK]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[ERROR]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
}

log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[WARN]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
}

log_alert() {
    local severity="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp,$severity,$message" >> "$ALERT_LOG"
    
    case "$severity" in
        "CRITICAL")
            echo -e "${RED}[ALERT-CRITICAL]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
            ;;
        "WARNING")
            echo -e "${YELLOW}[ALERT-WARNING]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
            ;;
        "INFO")
            echo -e "${BLUE}[ALERT-INFO]${NC} $timestamp - $message" | tee -a "$LOG_FILE"
            ;;
    esac
}

log_metrics() {
    local timestamp="$1"
    local endpoint="$2"
    local http_code="$3"
    local response_time="$4"
    local size="$5"
    
    echo "$timestamp,$endpoint,$http_code,$response_time,$size" >> "$METRICS_LOG"
}

check_health_endpoint() {
    local endpoint="$1"
    local timeout="${2:-10}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # Make request and capture response
    local response
    response=$(curl -s -w "%{http_code}:%{time_total}:%{size_download}" \
                   --max-time "$timeout" \
                   "$BASE_URL$endpoint" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        CONSECUTIVE_FAILURES=$((CONSECUTIVE_FAILURES + 1))
        log_error "$endpoint - Request failed (timeout or connection error)"
        log_metrics "$(date +%s)" "$endpoint" "0" "0" "0"
        return 1
    fi
    
    # Parse response
    local http_code time_total size_download
    IFS=':' read -r http_code time_total size_download <<< "$response"
    
    # Log metrics
    log_metrics "$(date +%s)" "$endpoint" "$http_code" "$time_total" "$size_download"
    
    # Check HTTP status
    if [ "$http_code" -eq 200 ]; then
        CONSECUTIVE_FAILURES=0
        log_success "$endpoint - HTTP $http_code - ${time_total}s - ${size_download} bytes"
        
        # Check response time threshold
        if (( $(echo "$time_total > $RESPONSE_TIME_THRESHOLD" | bc -l) )); then
            log_alert "WARNING" "$endpoint response time ${time_total}s exceeds threshold ${RESPONSE_TIME_THRESHOLD}s"
        fi
        
        return 0
    else
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        CONSECUTIVE_FAILURES=$((CONSECUTIVE_FAILURES + 1))
        log_error "$endpoint - HTTP $http_code - ${time_total}s"
        
        # Generate alerts based on status code
        case "$http_code" in
            "503")
                log_alert "CRITICAL" "$endpoint service unavailable (HTTP 503)"
                ;;
            "500"|"502"|"504")
                log_alert "CRITICAL" "$endpoint server error (HTTP $http_code)"
                ;;
            "404")
                log_alert "WARNING" "$endpoint not found (HTTP 404)"
                ;;
            *)
                log_alert "WARNING" "$endpoint returned HTTP $http_code"
                ;;
        esac
        
        return 1
    fi
}

check_all_endpoints() {
    local endpoints=("/health" "/health/ready" "/health/live")
    local all_healthy=true
    
    for endpoint in "${endpoints[@]}"; do
        if ! check_health_endpoint "$endpoint"; then
            all_healthy=false
        fi
    done
    
    # Check for consecutive failures
    if [ "$CONSECUTIVE_FAILURES" -ge "$CONSECUTIVE_FAILURES_THRESHOLD" ]; then
        log_alert "CRITICAL" "Service has failed $CONSECUTIVE_FAILURES consecutive health checks"
    fi
    
    return $all_healthy
}

collect_detailed_metrics() {
    log_info "Collecting detailed metrics..."
    
    # Get health metrics
    local health_response
    health_response=$(curl -s --max-time 10 "$BASE_URL/health" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$health_response" ]; then
        # Extract metrics if jq is available
        if command -v jq >/dev/null 2>&1; then
            local memory_usage cpu_usage uptime
            memory_usage=$(echo "$health_response" | jq -r '.system_metrics.memory_usage_percent // "N/A"')
            cpu_usage=$(echo "$health_response" | jq -r '.system_metrics.cpu_usage_percent // "N/A"')
            uptime=$(echo "$health_response" | jq -r '.uptime_seconds // "N/A"')
            
            log_info "System metrics - Memory: ${memory_usage}%, CPU: ${cpu_usage}%, Uptime: ${uptime}s"
            
            # Check thresholds
            if [ "$memory_usage" != "N/A" ] && (( $(echo "$memory_usage > 90" | bc -l) )); then
                log_alert "WARNING" "High memory usage: ${memory_usage}%"
            fi
            
            if [ "$cpu_usage" != "N/A" ] && (( $(echo "$cpu_usage > 80" | bc -l) )); then
                log_alert "WARNING" "High CPU usage: ${cpu_usage}%"
            fi
        fi
    fi
    
    # Get performance metrics
    local metrics_response
    metrics_response=$(curl -s --max-time 10 "$BASE_URL/health/metrics" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$metrics_response" ]; then
        log_info "Performance metrics collected successfully"
    else
        log_warning "Failed to collect performance metrics"
    fi
}

generate_status_report() {
    local current_time=$(date +%s)
    local uptime=$((current_time - START_TIME))
    local error_rate=0
    
    if [ "$TOTAL_CHECKS" -gt 0 ]; then
        error_rate=$((FAILED_CHECKS * 100 / TOTAL_CHECKS))
    fi
    
    echo ""
    echo "======================================"
    echo "Monitoring Status Report"
    echo "======================================"
    echo "Monitoring duration: ${uptime}s"
    echo "Total health checks: $TOTAL_CHECKS"
    echo "Failed checks: $FAILED_CHECKS"
    echo "Error rate: ${error_rate}%"
    echo "Consecutive failures: $CONSECUTIVE_FAILURES"
    echo "Log file: $LOG_FILE"
    echo "Metrics file: $METRICS_LOG"
    echo "Alerts file: $ALERT_LOG"
    echo "======================================"
    echo ""
}

cleanup() {
    log_info "Monitoring stopped"
    generate_status_report
    
    # Generate summary statistics
    if [ -f "$METRICS_LOG" ] && command -v awk >/dev/null 2>&1; then
        local avg_response_time
        avg_response_time=$(awk -F',' 'NR>1 && $4>0 {sum+=$4; count++} END {if(count>0) print sum/count; else print 0}' "$METRICS_LOG")
        log_info "Average response time: ${avg_response_time}s"
    fi
    
    exit 0
}

show_help() {
    cat << EOF
Usage: $0 [base_url] [interval] [duration]

Arguments:
  base_url   Base URL of the service (default: http://localhost:8081)
  interval   Check interval in seconds (default: 30)
  duration   Monitoring duration in seconds (default: 0 = infinite)

Examples:
  $0                                    # Monitor localhost every 30s indefinitely
  $0 http://localhost:8081 60          # Monitor every 60s indefinitely
  $0 http://localhost:8081 30 3600     # Monitor for 1 hour
  $0 https://api.example.com 15 7200   # Monitor production for 2 hours

Features:
  - Continuous health monitoring
  - Automatic alerting on failures
  - Response time monitoring
  - System metrics collection
  - Detailed logging and metrics

Files Generated:
  - health_monitor_[timestamp].log     # Main log file
  - alerts_[timestamp].log             # Alert log (CSV format)
  - metrics_[timestamp].log            # Metrics log (CSV format)

Thresholds:
  - Response time: ${RESPONSE_TIME_THRESHOLD}s
  - Error rate: ${ERROR_RATE_THRESHOLD}%
  - Consecutive failures: ${CONSECUTIVE_FAILURES_THRESHOLD}

Requirements:
  - curl (required)
  - jq (optional, for JSON parsing)
  - bc (optional, for floating point comparisons)
EOF
}

# Main monitoring loop
main() {
    echo "======================================"
    echo "Wellbot Bridge Continuous Monitor"
    echo "======================================"
    echo "Base URL: $BASE_URL"
    echo "Interval: ${INTERVAL}s"
    echo "Duration: $([ "$DURATION" -eq 0 ] && echo "Infinite" || echo "${DURATION}s")"
    echo "Started: $(date)"
    echo "======================================"
    echo ""
    
    # Initialize log files
    echo "timestamp,endpoint,http_code,response_time,size" > "$METRICS_LOG"
    echo "timestamp,severity,message" > "$ALERT_LOG"
    
    log_info "Monitoring started"
    
    local end_time=0
    if [ "$DURATION" -gt 0 ]; then
        end_time=$(($(date +%s) + DURATION))
    fi
    
    # Initial connectivity test
    if ! curl -s --max-time 5 "$BASE_URL/health" >/dev/null 2>&1; then
        log_alert "CRITICAL" "Initial connectivity test failed - service may be down"
    fi
    
    # Main monitoring loop
    while true; do
        # Check if duration limit reached
        if [ "$DURATION" -gt 0 ] && [ $(date +%s) -ge $end_time ]; then
            log_info "Monitoring duration reached, stopping"
            break
        fi
        
        # Perform health checks
        check_all_endpoints
        
        # Collect detailed metrics every 5th check
        if [ $((TOTAL_CHECKS % 5)) -eq 0 ]; then
            collect_detailed_metrics
        fi
        
        # Generate periodic status report
        if [ $((TOTAL_CHECKS % 20)) -eq 0 ]; then
            generate_status_report
        fi
        
        # Wait for next check
        sleep "$INTERVAL"
    done
    
    cleanup
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo "Error: Missing required dependencies: ${missing_deps[*]}"
        exit 1
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        echo "Warning: jq not found - JSON parsing will be limited"
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        echo "Warning: bc not found - floating point comparisons will be limited"
    fi
}

# Script entry point
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# Set up signal handlers
trap cleanup INT TERM

check_dependencies
main
