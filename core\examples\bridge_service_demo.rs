/*!
# 🌉 Wellbot Bridge Service Comprehensive Demo

A complete interactive demonstration of the Wellbot Bridge Service showcasing the full
integration of AI client, chat-port client, message processing, and JID authorization.
This demo provides a single entry point to experience all bridge service capabilities.

## Features
- ✨ Beautiful interactive CLI with cliclack
- 🤖 AI client integration for intelligent responses
- 💬 Chat-port client for WhatsApp communication
- 🔄 Message processing with rate limiting and filtering
- 🔐 JID authorization management
- 📊 Real-time statistics and monitoring
- 🚀 Background task support with tokio::spawn
- 🎯 Professional error handling and recovery
- 📈 Performance metrics and health monitoring
- 🔧 Comprehensive configuration management

## Usage
```bash
cargo run --example bridge_service_demo --features examples
```

## Prerequisites
- Chat-port service running (localhost:8081)
- Genuis AI service running (localhost:8000)
- WebSocket endpoint: ws://localhost:8081/ws
- HTTP API endpoints available
*/

use cliclack::{
    confirm, input, intro, log, multiselect, note, outro, outro_cancel, select, spinner,
};
use console::style;
use std::{sync::Arc, time::Duration};
use tokio::time::sleep;
use wellbot_bridge::{
    config::Config,
    services::{
        ai_client::AiClient,
        bridge_service::BridgeService,
        chat_port_client::ChatPortClient,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
        message_processor::MessageProcessor,
    },
    types::{ExplanationRequest, IncomingWhatsAppData, SendMessageRequest},
};

/// Demo operation modes
#[derive(Debug, Clone, Copy, Eq, PartialEq)]
enum DemoMode {
    QuickStart,
    FullDemo,
    ServiceTesting,
    JidManagement,
    MessageProcessing,
    AIIntegration,
    Monitoring,
}

/// Background task execution preference
#[derive(Debug, Clone, Copy)]
enum BackgroundMode {
    Always,
    Never,
    Ask,
}

/// Demo state for tracking services and statistics
#[derive(Debug)]
struct DemoState {
    config: Config,
    bridge_service: Option<Arc<BridgeService>>,
    ai_client: Option<Arc<AiClient>>,
    chat_client: Option<Arc<ChatPortClient>>,
    message_processor: Option<Arc<MessageProcessor>>,
    jid_authorization: Option<Arc<JidAuthorizationService>>,
    background_mode: BackgroundMode,
    messages_processed: u32,
    services_healthy: bool,
}

impl DemoState {
    fn new(config: Config) -> Self {
        Self {
            config,
            bridge_service: None,
            ai_client: None,
            chat_client: None,
            message_processor: None,
            jid_authorization: None,
            background_mode: BackgroundMode::Ask,
            messages_processed: 0,
            services_healthy: false,
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Set up graceful shutdown
    ctrlc::set_handler(move || {
        println!("\n🛑 Received interrupt signal - shutting down gracefully...");
        std::process::exit(0);
    })
    .expect("setting Ctrl-C handler");

    // Beautiful intro
    cliclack::clear_screen()?;
    intro(style(" 🌉 Wellbot Bridge Service Demo ").on_cyan().black())?;

    log::info("Welcome to the comprehensive Wellbot Bridge Service demonstration!")?;
    log::remark("This demo showcases the complete bridge service integration")?;

    // Prerequisites check
    display_prerequisites()?;

    // Configuration setup
    let config = setup_configuration().await?;
    let mut demo_state = DemoState::new(config);

    // Background mode preference
    demo_state.background_mode = select_background_mode()?;

    // Main demo loop
    loop {
        let demo_mode = select_demo_mode()?;

        match demo_mode {
            DemoMode::QuickStart => run_quick_start_demo(&mut demo_state).await?,
            DemoMode::FullDemo => run_full_demo(&mut demo_state).await?,
            DemoMode::ServiceTesting => run_service_testing(&mut demo_state).await?,
            DemoMode::JidManagement => run_jid_management_demo(&mut demo_state).await?,
            DemoMode::MessageProcessing => run_message_processing_demo(&mut demo_state).await?,
            DemoMode::AIIntegration => run_ai_integration_demo(&demo_state).await?,
            DemoMode::Monitoring => run_monitoring_demo(&demo_state).await?,
        }

        if !confirm("🔄 Continue with another demo mode?").interact()? {
            break;
        }
    }

    // Cleanup and shutdown
    cleanup_demo(&demo_state).await?;

    outro(format!(
        "🎉 Thanks for exploring the Wellbot Bridge Service!\n\n{}",
        style("For more information, visit: https://github.com/wellbot/wellbot")
            .cyan()
            .underlined()
    ))?;

    Ok(())
}

/// Display prerequisites and service requirements
fn display_prerequisites() -> Result<(), Box<dyn std::error::Error>> {
    note(
        "📋 Prerequisites & Services",
        "This demo requires the following services to be running:\n\n🤖 Genuis AI Service:\n  • URL: http://localhost:8000\n  • Start: cd genuis && uv run asgi.py\n\n💬 Chat-port Service:\n  • WebSocket: ws://localhost:8081/ws\n  • HTTP API: http://localhost:8081/api\n  • Start: cd chat-port && go run cmd/main.go\n\n🔐 JID Authorization:\n  • Persistent storage enabled\n  • Configuration from environment or defaults",
    )?;

    if !confirm("🚀 Are all required services running?")
        .initial_value(false)
        .interact()?
    {
        note(
            "💡 Service Startup Guide",
            "Please start the required services before continuing:\n\n1. AI Service: cd genuis && uv run asgi.py\n2. Chat-port: cd chat-port && go run cmd/main.go\n3. Then restart this demo",
        )?;
        outro_cancel("Demo cancelled - please start required services first")?;
        std::process::exit(1);
    }

    Ok(())
}

/// Setup configuration with user preferences
async fn setup_configuration() -> Result<Config, Box<dyn std::error::Error>> {
    let config_choice = select("How would you like to configure the bridge service?")
        .initial_value("default")
        .item(
            "default",
            "Use default configuration",
            "Recommended for testing",
        )
        .item("env", "Load from environment", "Production-like setup")
        .item("custom", "Custom configuration", "Advanced users")
        .interact()?;

    let config = match config_choice {
        "env" => {
            log::step("Loading configuration from environment variables...")?;
            match Config::from_env() {
                Ok(config) => {
                    log::success("Configuration loaded from environment!")?;
                    config
                }
                Err(e) => {
                    log::warning(format!("Failed to load from env: {}", e))?;
                    log::remark("Falling back to default configuration")?;
                    Config::default()
                }
            }
        }
        "custom" => {
            log::step("Setting up custom configuration...")?;
            setup_custom_configuration().await?
        }
        _ => {
            log::step("Using default configuration")?;
            Config::default()
        }
    };

    display_config_summary(&config)?;
    Ok(config)
}

/// Setup custom configuration interactively
async fn setup_custom_configuration() -> Result<Config, Box<dyn std::error::Error>> {
    let mut config = Config::default();

    // AI Service configuration
    if confirm("🤖 Configure AI service settings?").interact()? {
        let base_url: String = input("AI service base URL")
            .placeholder("http://localhost:8000")
            .default_input("http://localhost:8000")
            .interact()?;
        config.ai_service.base_url = base_url;

        let timeout: String = input("Request timeout (seconds)")
            .placeholder("30")
            .default_input("30")
            .interact()?;
        config.ai_service.timeout_secs = timeout.parse().unwrap_or(30);
    }

    // Chat-port configuration
    if confirm("💬 Configure chat-port settings?").interact()? {
        let ws_url: String = input("WebSocket URL")
            .placeholder("ws://localhost:8081/ws")
            .default_input("ws://localhost:8081/ws")
            .interact()?;
        config.chat_port.websocket_url = ws_url;

        let api_url: String = input("HTTP API URL")
            .placeholder("http://localhost:8081/api")
            .default_input("http://localhost:8081/api")
            .interact()?;
        config.chat_port.api_base_url = api_url;
    }

    // Bridge configuration
    if confirm("🌉 Configure bridge processing settings?").interact()? {
        let rate_limit: String = input("Rate limit (messages per minute)")
            .placeholder("60")
            .default_input("60")
            .interact()?;
        config.bridge.rate_limit_per_minute = rate_limit.parse().unwrap_or(60);

        let max_concurrent: String = input("Max concurrent messages")
            .placeholder("10")
            .default_input("10")
            .interact()?;
        config.bridge.max_concurrent_messages = max_concurrent.parse().unwrap_or(10);
    }

    log::success("Custom configuration completed!")?;
    Ok(config)
}

/// Display configuration summary
fn display_config_summary(config: &Config) -> Result<(), Box<dyn std::error::Error>> {
    let config_info = format!(
        "🤖 AI Service:\n  • URL: {}\n  • Timeout: {}s\n  • Retries: {}\n\n💬 Chat-port:\n  • WebSocket: {}\n  • HTTP API: {}\n  • Timeout: {}s\n\n🌉 Bridge:\n  • Rate Limit: {}/min\n  • Max Concurrent: {}\n  • Health Port: {}",
        style(&config.ai_service.base_url).cyan(),
        style(config.ai_service.timeout_secs).green(),
        style(config.ai_service.retry_attempts).green(),
        style(&config.chat_port.websocket_url).cyan(),
        style(&config.chat_port.api_base_url).cyan(),
        style(config.chat_port.connection_timeout_secs).green(),
        style(config.bridge.rate_limit_per_minute).yellow(),
        style(config.bridge.max_concurrent_messages).yellow(),
        style(config.health.port).blue()
    );

    note("⚙️ Configuration Summary", config_info)?;
    Ok(())
}

/// Select background task execution mode
fn select_background_mode() -> Result<BackgroundMode, Box<dyn std::error::Error>> {
    let mode = select("🚀 Background Task Execution Mode")
        .initial_value("ask")
        .item("ask", "🔄 Interactive Mode", "Ask for each operation")
        .item("always", "⚡ Background Mode", "Always use tokio::spawn")
        .item(
            "never",
            "🎯 Synchronous Mode",
            "Execute tasks synchronously",
        )
        .interact()?;

    let background_mode = match mode {
        "always" => {
            log::success("⚡ Background mode enabled - All tasks will use tokio::spawn")?;
            BackgroundMode::Always
        }
        "never" => {
            log::info("🎯 Synchronous mode enabled - Tasks will run sequentially")?;
            BackgroundMode::Never
        }
        _ => {
            log::info("🔄 Interactive mode enabled - You'll be asked for each operation")?;
            BackgroundMode::Ask
        }
    };

    Ok(background_mode)
}

/// Select demo mode
fn select_demo_mode() -> Result<DemoMode, Box<dyn std::error::Error>> {
    let mode = select("What would you like to demonstrate?")
        .initial_value(DemoMode::QuickStart)
        .item(
            DemoMode::QuickStart,
            "🚀 Quick Start",
            "Initialize services and run basic tests",
        )
        .item(
            DemoMode::FullDemo,
            "🌟 Full Demo",
            "Complete bridge service workflow demonstration",
        )
        .item(
            DemoMode::ServiceTesting,
            "🔬 Service Testing",
            "Test individual service components",
        )
        .item(
            DemoMode::JidManagement,
            "🔐 JID Management",
            "Manage authorized WhatsApp accounts",
        )
        .item(
            DemoMode::MessageProcessing,
            "🔄 Message Processing",
            "Demonstrate message filtering and processing",
        )
        .item(
            DemoMode::AIIntegration,
            "🤖 AI Integration",
            "Showcase AI-powered message responses",
        )
        .item(
            DemoMode::Monitoring,
            "📊 Monitoring",
            "Real-time statistics and health monitoring",
        )
        .interact()?;

    Ok(mode)
}

/// Run quick start demo - initialize services and basic functionality
async fn run_quick_start_demo(
    demo_state: &mut DemoState,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🚀 Quick Start Demo - Initializing Bridge Service")?;

    // Initialize JID authorization service
    let spinner = spinner();
    spinner.start("🔐 Initializing JID authorization service...");
    let jid_config = JidAuthConfig::from_env().unwrap_or_default();
    let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);
    demo_state.jid_authorization = Some(jid_authorization.clone());
    spinner.stop("✅ JID authorization service ready");

    // Initialize bridge service
    spinner.start("🌉 Initializing bridge service...");
    let bridge_service =
        Arc::new(BridgeService::new(demo_state.config.clone(), jid_authorization.clone()).await?);
    demo_state.bridge_service = Some(bridge_service.clone());
    spinner.stop("✅ Bridge service initialized");

    // Quick health check
    spinner.start("🏥 Performing health checks...");
    let health_status = bridge_service.get_health_status().await?;
    demo_state.services_healthy = health_status.status.is_operational();

    if demo_state.services_healthy {
        spinner.stop("✅ All services healthy");
        log::success("Bridge service is ready for operation!")?;
    } else {
        spinner.stop("⚠️ Some services may have issues");
        log::warning("Some services may not be fully operational")?;
    }

    // Add a default JID for testing
    let default_jid: String = input("Enter a WhatsApp JID for testing")
        .placeholder("<EMAIL>")
        .default_input("<EMAIL>")
        .interact()?;

    jid_authorization
        .add_jid(default_jid.clone(), Some("Demo User".to_string()))
        .await?;
    log::success(format!("✅ Added JID to authorized list: {}", default_jid))?;

    // Quick message processing test
    if confirm("🔄 Test message processing?").interact()? {
        let test_message = IncomingWhatsAppData {
            from: default_jid,
            message: "Hello, this is a test message!".to_string(),
            message_id: format!("test_{}", chrono::Utc::now().timestamp()),
            timestamp: chrono::Utc::now(),
        };

        spinner.start("🔄 Processing test message...");
        match bridge_service.process_whatsapp_message(test_message).await {
            Ok(()) => {
                spinner.stop("✅ Message processed successfully!");
                demo_state.messages_processed += 1;
                log::success("Bridge service is working correctly!")?;
            }
            Err(e) => {
                spinner.stop("❌ Message processing failed");
                log::warning(format!("Processing error: {}", e))?;
            }
        }
    }

    display_quick_start_summary(demo_state)?;
    Ok(())
}

/// Run full comprehensive demo
async fn run_full_demo(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🌟 Full Bridge Service Demo")?;

    // Ensure services are initialized
    if demo_state.bridge_service.is_none() {
        run_quick_start_demo(demo_state).await?;
    }

    let bridge_service = demo_state.bridge_service.as_ref().unwrap();

    // Comprehensive workflow demonstration
    note(
        "🌟 Full Demo Workflow",
        "This demo will showcase:\n• JID authorization management\n• Message processing with AI integration\n• Real-time monitoring and statistics\n• Error handling and recovery\n• Performance optimization",
    )?;

    // Step 1: JID Management
    log::info("Step 1: JID Authorization Management")?;
    let jid_auth = demo_state.jid_authorization.as_ref().unwrap();

    // Add multiple test JIDs
    let test_jids = vec![
        ("<EMAIL>", "Test User 1"),
        ("<EMAIL>", "Test User 2"),
        ("<EMAIL>", "Admin User"),
    ];

    for (jid, name) in &test_jids {
        jid_auth
            .add_jid(jid.to_string(), Some(name.to_string()))
            .await?;
        log::success(format!("✅ Added JID: {} ({})", jid, name))?;
    }

    // Step 2: Message Processing Demo
    log::info("Step 2: Message Processing with AI Integration")?;
    let test_messages = vec![
        ("<EMAIL>", "Explain quantum computing"),
        ("<EMAIL>", "What is Rust programming language?"),
        ("<EMAIL>", "How does machine learning work?"),
        ("<EMAIL>", "This should be rejected"),
    ];

    for (from, message) in test_messages {
        let whatsapp_data = IncomingWhatsAppData {
            from: from.to_string(),
            message: message.to_string(),
            message_id: format!(
                "demo_{}_{}",
                from.replace("@s.whatsapp.net", ""),
                chrono::Utc::now().timestamp_nanos_opt().unwrap()
            ),
            timestamp: chrono::Utc::now(),
        };

        let spinner = spinner();
        spinner.start(format!("🔄 Processing message from {}", from));

        match bridge_service.process_whatsapp_message(whatsapp_data).await {
            Ok(()) => {
                spinner.stop(format!("✅ Message from {} processed", from));
                demo_state.messages_processed += 1;
            }
            Err(e) => {
                spinner.stop(format!("❌ Message from {} failed", from));
                log::warning(format!("Error: {}", e))?;
            }
        }

        // Brief pause between messages
        sleep(Duration::from_millis(500)).await;
    }

    // Step 3: Statistics and Monitoring
    log::info("Step 3: Statistics and Monitoring")?;
    display_comprehensive_stats(demo_state, bridge_service).await?;

    // Step 4: Performance Testing
    if confirm("🚀 Run performance testing?").interact()? {
        let bridge_service_clone = bridge_service.clone();
        run_performance_test(demo_state, &bridge_service_clone).await?;
    }

    log::success("🌟 Full demo completed successfully!")?;
    Ok(())
}

/// Run service testing demo
async fn run_service_testing(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔬 Service Testing Demo")?;

    let services_to_test = multiselect("Select services to test:")
        .initial_values(vec!["ai", "chat", "jid"])
        .item(
            "ai",
            "🤖 AI Client",
            "Test AI service connectivity and responses",
        )
        .item("chat", "💬 Chat-port Client", "Test WebSocket and HTTP API")
        .item("jid", "🔐 JID Authorization", "Test authorization service")
        .item(
            "bridge",
            "🌉 Bridge Service",
            "Test complete bridge integration",
        )
        .interact()?;

    for service in services_to_test {
        match service {
            "ai" => test_ai_service(demo_state).await?,
            "chat" => test_chat_service(demo_state).await?,
            "jid" => test_jid_service(demo_state).await?,
            "bridge" => test_bridge_service(demo_state).await?,
            _ => {}
        }
    }

    Ok(())
}

/// Test AI service functionality
async fn test_ai_service(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🤖 Testing AI Service")?;

    // Initialize AI client if not already done
    if demo_state.ai_client.is_none() {
        let spinner = spinner();
        spinner.start("🔧 Initializing AI client...");
        match AiClient::new(demo_state.config.ai_service.clone()) {
            Ok(client) => {
                demo_state.ai_client = Some(Arc::new(client));
                spinner.stop("✅ AI client initialized");
            }
            Err(e) => {
                spinner.stop("❌ AI client initialization failed");
                log::error(format!("Error: {}", e))?;
                return Ok(());
            }
        }
    }

    let ai_client = demo_state.ai_client.as_ref().unwrap();

    // Health check
    let spinner = spinner();
    spinner.start("🏥 Checking AI service health...");
    match ai_client.health_check().await {
        Ok(is_healthy) => {
            if is_healthy {
                spinner.stop("✅ AI service is healthy");
            } else {
                spinner.stop("⚠️ AI service health check failed");
            }
        }
        Err(e) => {
            spinner.stop("❌ Health check error");
            log::error(format!("Health check failed: {}", e))?;
        }
    }

    // Test explanation generation
    if confirm("🧠 Test AI explanation generation?").interact()? {
        let test_prompt = input("Enter test prompt")
            .placeholder("Explain artificial intelligence")
            .default_input("Explain artificial intelligence")
            .interact()?;

        let request = ExplanationRequest {
            prompt: test_prompt,
            temperature: Some(0.7),
            max_tokens: Some(500i32),
        };

        spinner.start("🤖 Generating AI explanation...");
        match ai_client.generate_explanation(request).await {
            Ok(response) => {
                spinner.stop("✅ Explanation generated successfully");
                let preview = if response.content.len() > 200 {
                    format!("{}...", &response.content[..200])
                } else {
                    response.content.clone()
                };
                log::success(format!("Response preview: {}", preview))?;
                log::info(format!(
                    "Tokens used: {}, Processing time: {:.2}ms",
                    response.tokens_used, response.processing_time_ms
                ))?;
            }
            Err(e) => {
                spinner.stop("❌ Explanation generation failed");
                log::error(format!("Error: {}", e))?;
            }
        }
    }

    Ok(())
}

/// Test chat-port service functionality
async fn test_chat_service(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("💬 Testing Chat-port Service")?;

    // Initialize chat client if not already done
    if demo_state.chat_client.is_none() {
        let spinner = spinner();
        spinner.start("🔧 Initializing chat-port client...");
        match ChatPortClient::new(demo_state.config.chat_port.clone()) {
            Ok(client) => {
                demo_state.chat_client = Some(Arc::new(client));
                spinner.stop("✅ Chat-port client initialized");
            }
            Err(e) => {
                spinner.stop("❌ Chat-port client initialization failed");
                log::error(format!("Error: {}", e))?;
                return Ok(());
            }
        }
    }

    let chat_client = demo_state.chat_client.as_ref().unwrap();

    // Test connection
    let spinner = spinner();
    spinner.start("🔗 Testing chat-port connection...");
    match chat_client.health_check().await {
        Ok(is_healthy) => {
            if is_healthy {
                spinner.stop("✅ Chat-port connection successful");
            } else {
                spinner.stop("⚠️ Chat-port service is unhealthy");
            }
        }
        Err(e) => {
            spinner.stop("❌ Chat-port connection failed");
            log::error(format!("Connection error: {}", e))?;
        }
    }

    // Test message sending
    if confirm("📤 Test message sending?").interact()? {
        let recipient = input("Enter recipient number")
            .placeholder("**********")
            .interact()?;

        let message_text = input("Enter test message")
            .placeholder("Hello from Wellbot Bridge!")
            .default_input("Hello from Wellbot Bridge!")
            .interact()?;

        let send_request = SendMessageRequest {
            number: recipient,
            message: message_text,
        };

        spinner.start("📤 Sending test message...");
        match chat_client.send_message(send_request).await {
            Ok(_response) => {
                spinner.stop("✅ Message sent successfully");
                log::success("Chat-port service is working correctly!")?;
            }
            Err(e) => {
                spinner.stop("❌ Message sending failed");
                log::error(format!("Send error: {}", e))?;
            }
        }
    }

    Ok(())
}

/// Test JID authorization service
async fn test_jid_service(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔐 Testing JID Authorization Service")?;

    // Initialize JID service if not already done
    if demo_state.jid_authorization.is_none() {
        let spinner = spinner();
        spinner.start("🔧 Initializing JID authorization service...");
        let jid_config = JidAuthConfig::from_env().unwrap_or_default();
        let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);
        demo_state.jid_authorization = Some(jid_authorization);
        spinner.stop("✅ JID authorization service initialized");
    }

    let jid_auth = demo_state.jid_authorization.as_ref().unwrap();

    // Test JID operations
    let test_jid = "<EMAIL>";

    // Add JID
    log::info("Testing JID addition...")?;
    match jid_auth
        .add_jid(test_jid.to_string(), Some("Test User".to_string()))
        .await
    {
        Ok(was_new) => {
            if was_new {
                log::success(format!("✅ New JID added: {}", test_jid))?;
            } else {
                log::info(format!("✅ JID reactivated: {}", test_jid))?;
            }
        }
        Err(e) => {
            log::error(format!("Failed to add JID: {}", e))?;
        }
    }

    // Check authorization
    log::info("Testing JID authorization check...")?;
    let is_authorized = jid_auth.is_authorized(test_jid).await;
    if is_authorized {
        log::success(format!("✅ JID {} is authorized", test_jid))?;
    } else {
        log::warning(format!("❌ JID {} is not authorized", test_jid))?;
    }

    // List authorized JIDs
    log::info("Listing authorized JIDs...")?;
    let authorized_jids = jid_auth.get_authorized_jids().await;
    log::info(format!(
        "📋 Authorized JIDs ({}): {:?}",
        authorized_jids.len(),
        authorized_jids
    ))?;

    // Get statistics
    let stats = jid_auth.get_stats().await;
    log::info(format!(
        "📊 JID Stats - Total: {}, Active: {}, Inactive: {}",
        stats.total_jids, stats.active_jids, stats.inactive_jids
    ))?;

    // Test unauthorized JID
    let unauthorized_jid = "<EMAIL>";
    let is_unauthorized = jid_auth.is_authorized(unauthorized_jid).await;
    if !is_unauthorized {
        log::success(format!(
            "✅ Correctly rejected unauthorized JID: {}",
            unauthorized_jid
        ))?;
    } else {
        log::warning(format!(
            "❌ Incorrectly authorized JID: {}",
            unauthorized_jid
        ))?;
    }

    Ok(())
}

/// Test complete bridge service integration
async fn test_bridge_service(demo_state: &mut DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🌉 Testing Complete Bridge Service Integration")?;

    // Ensure bridge service is initialized
    if demo_state.bridge_service.is_none() {
        run_quick_start_demo(demo_state).await?;
    }

    let bridge_service = demo_state.bridge_service.as_ref().unwrap();

    // Comprehensive integration test
    log::info("Running comprehensive integration test...")?;

    // Test 1: Health status
    let spinner = spinner();
    spinner.start("🏥 Checking overall health status...");
    let health_status = bridge_service.get_health_status().await?;
    spinner.stop(format!("Health Status: {:?}", health_status.status));

    log::info(format!("🏥 Overall Health: {:?}", health_status.status))?;
    log::info(format!(
        "📊 Components: {} total, {} healthy, {} critical",
        health_status.health_summary.total_components,
        health_status.health_summary.healthy_components,
        health_status.health_summary.critical_components
    ))?;

    // Test 2: Message processing workflow
    if confirm("🔄 Test complete message processing workflow?").interact()? {
        let test_scenarios = vec![
            (
                "<EMAIL>",
                "Explain blockchain technology",
                true,
            ),
            (
                "<EMAIL>",
                "What is machine learning?",
                true,
            ),
            (
                "<EMAIL>",
                "This should be rejected",
                false,
            ),
        ];

        // Add authorized JIDs first
        let jid_auth = demo_state.jid_authorization.as_ref().unwrap();
        jid_auth
            .add_jid(
                "<EMAIL>".to_string(),
                Some("Authorized User".to_string()),
            )
            .await?;
        jid_auth
            .add_jid(
                "<EMAIL>".to_string(),
                Some("Another User".to_string()),
            )
            .await?;

        for (from, message, should_succeed) in test_scenarios {
            let whatsapp_data = IncomingWhatsAppData {
                from: from.to_string(),
                message: message.to_string(),
                message_id: format!(
                    "integration_test_{}",
                    chrono::Utc::now().timestamp_nanos_opt().unwrap()
                ),
                timestamp: chrono::Utc::now(),
            };

            spinner.start(format!("🔄 Processing message from {}", from));

            match bridge_service.process_whatsapp_message(whatsapp_data).await {
                Ok(()) => {
                    if should_succeed {
                        spinner.stop(format!("✅ Message from {} processed successfully", from));
                        demo_state.messages_processed += 1;
                    } else {
                        spinner.stop(format!("⚠️ Unexpected success for {}", from));
                        log::warning("Message should have been rejected but was processed")?;
                    }
                }
                Err(e) => {
                    if !should_succeed {
                        spinner.stop(format!("✅ Message from {} correctly rejected", from));
                    } else {
                        spinner.stop(format!("❌ Message from {} failed unexpectedly", from));
                        log::error(format!("Unexpected error: {}", e))?;
                    }
                }
            }

            sleep(Duration::from_millis(300)).await;
        }
    }

    // Test 3: Statistics and monitoring
    log::info("📊 Displaying bridge service statistics...")?;
    let bridge_stats = bridge_service.get_stats().await;
    log::info(format!(
        "Messages processed: {}",
        bridge_stats.messages_processed
    ))?;
    log::info(format!(
        "Messages unauthorized: {}",
        bridge_stats.messages_unauthorized
    ))?;
    log::info(format!(
        "AI requests sent: {}",
        bridge_stats.ai_requests_sent
    ))?;

    log::success("🌉 Bridge service integration test completed!")?;
    Ok(())
}

/// Run JID management demo
async fn run_jid_management_demo(
    demo_state: &mut DemoState,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔐 JID Management Demo")?;

    // Initialize JID service if needed
    if demo_state.jid_authorization.is_none() {
        let spinner = spinner();
        spinner.start("🔧 Initializing JID authorization service...");
        let jid_config = JidAuthConfig::from_env().unwrap_or_default();
        let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);
        demo_state.jid_authorization = Some(jid_authorization);
        spinner.stop("✅ JID authorization service initialized");
    }

    let jid_auth = demo_state.jid_authorization.as_ref().unwrap();

    loop {
        let action = select("JID Management Actions")
            .initial_value("list")
            .item(
                "list",
                "📋 List Authorized JIDs",
                "View all authorized accounts",
            )
            .item("add", "➕ Add JID", "Authorize a new WhatsApp account")
            .item("remove", "➖ Remove JID", "Deauthorize an account")
            .item(
                "check",
                "🔍 Check Authorization",
                "Test if a JID is authorized",
            )
            .item(
                "stats",
                "📊 View Statistics",
                "Display JID service statistics",
            )
            .item("back", "🔙 Back to Main Menu", "Return to main demo menu")
            .interact()?;

        match action {
            "list" => {
                let authorized_jids = jid_auth.get_authorized_jids().await;
                if authorized_jids.is_empty() {
                    log::warning("No authorized JIDs found")?;
                } else {
                    log::info(format!("📋 Authorized JIDs ({}):", authorized_jids.len()))?;
                    for (i, jid) in authorized_jids.iter().enumerate() {
                        log::info(format!("  {}. {}", i + 1, jid))?;
                    }
                }
            }
            "add" => {
                let jid: String = input("Enter WhatsApp JID to authorize")
                    .placeholder("<EMAIL>")
                    .validate(|input: &String| {
                        if input.trim().is_empty() {
                            Err("JID cannot be empty")
                        } else if !input.contains("@") {
                            Err("JID must contain @ symbol")
                        } else {
                            Ok(())
                        }
                    })
                    .interact()?;

                let display_name: String = input("Enter display name (optional)")
                    .placeholder("User Name")
                    .interact()?;

                let name = if display_name.trim().is_empty() {
                    None
                } else {
                    Some(display_name)
                };

                match jid_auth.add_jid(jid.clone(), name).await {
                    Ok(was_new) => {
                        if was_new {
                            log::success(format!("✅ New JID authorized: {}", jid))?;
                        } else {
                            log::info(format!("✅ JID reactivated: {}", jid))?;
                        }
                    }
                    Err(e) => {
                        log::error(format!("Failed to add JID: {}", e))?;
                    }
                }
            }
            "remove" => {
                let authorized_jids = jid_auth.get_authorized_jids().await;
                if authorized_jids.is_empty() {
                    log::warning("No authorized JIDs to remove")?;
                    continue;
                }

                let jid: String = input("Enter JID to remove")
                    .placeholder(&authorized_jids[0])
                    .interact()?;

                match jid_auth.remove_jid(&jid).await {
                    Ok(was_removed) => {
                        if was_removed {
                            log::success(format!("✅ JID removed: {}", jid))?;
                        } else {
                            log::warning(format!("JID not found: {}", jid))?;
                        }
                    }
                    Err(e) => {
                        log::error(format!("Failed to remove JID: {}", e))?;
                    }
                }
            }
            "check" => {
                let jid: String = input("Enter JID to check")
                    .placeholder("<EMAIL>")
                    .interact()?;

                let is_authorized = jid_auth.is_authorized(&jid).await;
                if is_authorized {
                    log::success(format!("✅ JID {} is authorized", jid))?;
                } else {
                    log::warning(format!("❌ JID {} is not authorized", jid))?;
                }
            }
            "stats" => {
                let stats = jid_auth.get_stats().await;
                let stats_info = format!(
                    "📊 JID Authorization Statistics:\n• Total JIDs: {}\n• Active JIDs: {}\n• Inactive JIDs: {}\n• Messages Processed: {}",
                    stats.total_jids,
                    stats.active_jids,
                    stats.inactive_jids,
                    stats.total_messages_processed
                );
                note("📊 JID Statistics", stats_info)?;
            }
            "back" => break,
            _ => {}
        }

        if action != "back" && !confirm("Continue with JID management?").interact()? {
            break;
        }
    }

    Ok(())
}

/// Run message processing demo
async fn run_message_processing_demo(
    demo_state: &mut DemoState,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔄 Message Processing Demo")?;

    // Ensure services are initialized
    if demo_state.bridge_service.is_none() {
        run_quick_start_demo(demo_state).await?;
    }

    let bridge_service = demo_state.bridge_service.as_ref().unwrap();
    let jid_auth = demo_state.jid_authorization.as_ref().unwrap();

    // Ensure we have some authorized JIDs
    let authorized_jids = jid_auth.get_authorized_jids().await;
    if authorized_jids.is_empty() {
        log::warning("No authorized JIDs found. Adding a test JID...")?;
        let test_jid = "<EMAIL>";
        jid_auth
            .add_jid(test_jid.to_string(), Some("Demo User".to_string()))
            .await?;
        log::success(format!("✅ Added test JID: {}", test_jid))?;
    }

    let processing_modes = multiselect("Select message processing scenarios:")
        .initial_values(vec!["authorized", "unauthorized"])
        .item(
            "authorized",
            "✅ Authorized Messages",
            "Test messages from authorized JIDs",
        )
        .item(
            "unauthorized",
            "❌ Unauthorized Messages",
            "Test rejection of unauthorized JIDs",
        )
        .item(
            "rate_limit",
            "🚦 Rate Limiting",
            "Test rate limiting behavior",
        )
        .item(
            "ai_integration",
            "🤖 AI Integration",
            "Test AI-powered responses",
        )
        .item("batch", "📦 Batch Processing", "Process multiple messages")
        .interact()?;

    for mode in processing_modes {
        match mode {
            "authorized" => test_authorized_messages(demo_state, bridge_service).await?,
            "unauthorized" => test_unauthorized_messages(demo_state, bridge_service).await?,
            "rate_limit" => test_rate_limiting(demo_state, bridge_service).await?,
            "ai_integration" => test_ai_integration_processing(demo_state, bridge_service).await?,
            "batch" => test_batch_processing(demo_state, bridge_service).await?,
            _ => {}
        }
    }

    Ok(())
}

/// Test authorized message processing
async fn test_authorized_messages(
    demo_state: &DemoState,
    bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("✅ Testing Authorized Message Processing")?;

    let jid_auth = demo_state.jid_authorization.as_ref().unwrap();
    let authorized_jids = jid_auth.get_authorized_jids().await;

    if authorized_jids.is_empty() {
        log::warning("No authorized JIDs available for testing")?;
        return Ok(());
    }

    let test_jid = &authorized_jids[0];
    let test_messages = [
        "Hello, this is a test message",
        "Can you explain quantum computing?",
        "What is the weather like today?",
    ];

    for (i, message) in test_messages.iter().enumerate() {
        let whatsapp_data = IncomingWhatsAppData {
            from: test_jid.clone(),
            message: message.to_string(),
            message_id: format!("auth_test_{}_{}", i, chrono::Utc::now().timestamp()),
            timestamp: chrono::Utc::now(),
        };

        let spinner = spinner();
        spinner.start(format!(
            "🔄 Processing authorized message {}/{}",
            i + 1,
            test_messages.len()
        ));

        match bridge_service.process_whatsapp_message(whatsapp_data).await {
            Ok(()) => {
                spinner.stop(format!(
                    "✅ Message {}/{} processed successfully",
                    i + 1,
                    test_messages.len()
                ));
                // Note: In a real implementation, we'd update demo_state here
                // but since we can't mutate it, we'll just log the success
            }
            Err(e) => {
                spinner.stop(format!(
                    "❌ Message {}/{} failed",
                    i + 1,
                    test_messages.len()
                ));
                log::error(format!("Processing error: {}", e))?;
            }
        }

        sleep(Duration::from_millis(500)).await;
    }

    log::success("✅ Authorized message processing test completed")?;
    Ok(())
}

// Placeholder functions for remaining demo modes
async fn test_unauthorized_messages(
    _demo_state: &DemoState,
    bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("❌ Testing Unauthorized Message Processing")?;

    let unauthorized_jids = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ];

    for (i, jid) in unauthorized_jids.iter().enumerate() {
        let whatsapp_data = IncomingWhatsAppData {
            from: jid.to_string(),
            message: "This message should be rejected".to_string(),
            message_id: format!("unauth_test_{}_{}", i, chrono::Utc::now().timestamp()),
            timestamp: chrono::Utc::now(),
        };

        let spinner = spinner();
        spinner.start(format!("🔄 Testing unauthorized message from {}", jid));

        match bridge_service.process_whatsapp_message(whatsapp_data).await {
            Ok(()) => {
                spinner.stop(format!("⚠️ Unexpected success for {}", jid));
                log::warning("Message should have been rejected but was processed")?;
            }
            Err(_) => {
                spinner.stop(format!("✅ Message from {} correctly rejected", jid));
            }
        }

        sleep(Duration::from_millis(300)).await;
    }

    log::success("❌ Unauthorized message processing test completed")?;
    Ok(())
}

async fn test_rate_limiting(
    _demo_state: &DemoState,
    _bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("🚦 Rate limiting test would be implemented here")?;
    log::remark(
        "This would test the rate limiting functionality by sending many messages quickly",
    )?;
    Ok(())
}

async fn test_ai_integration_processing(
    _demo_state: &DemoState,
    _bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("🤖 AI integration processing test would be implemented here")?;
    log::remark("This would test AI-powered message responses and processing")?;
    Ok(())
}

async fn test_batch_processing(
    _demo_state: &DemoState,
    _bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("📦 Batch processing test would be implemented here")?;
    log::remark("This would test processing multiple messages concurrently")?;
    Ok(())
}

async fn run_ai_integration_demo(
    _demo_state: &DemoState,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🤖 AI Integration Demo")?;
    log::remark("This would showcase AI client integration and intelligent responses")?;
    Ok(())
}

async fn run_monitoring_demo(demo_state: &DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("📊 Monitoring and Statistics Demo")?;

    if let Some(bridge_service) = &demo_state.bridge_service {
        display_comprehensive_stats(demo_state, bridge_service).await?;
    } else {
        log::warning("Bridge service not initialized. Run Quick Start first.")?;
    }

    Ok(())
}

async fn run_performance_test(
    _demo_state: &mut DemoState,
    _bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    log::info("🚀 Performance testing would be implemented here")?;
    log::remark("This would test concurrent message processing and performance metrics")?;
    Ok(())
}

async fn display_comprehensive_stats(
    demo_state: &DemoState,
    bridge_service: &BridgeService,
) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("📊 Gathering comprehensive statistics...");

    // Bridge service stats
    let bridge_stats = bridge_service.get_stats().await;
    let health_status = bridge_service.get_health_status().await?;

    // JID authorization stats
    let jid_stats = if let Some(jid_auth) = &demo_state.jid_authorization {
        Some(jid_auth.get_stats().await)
    } else {
        None
    };

    spinner.stop("📊 Statistics gathered");

    let stats_info = format!(
        "🌉 Bridge Service Statistics:\n• Messages Processed: {}\n• Messages Unauthorized: {}\n• AI Requests Sent: {}\n• Overall Health: {:?}\n\n🔐 JID Authorization:\n• Total JIDs: {}\n• Active JIDs: {}\n• Messages Processed: {}\n\n📈 Demo Session:\n• Messages Processed: {}\n• Services Healthy: {}",
        bridge_stats.messages_processed,
        bridge_stats.messages_unauthorized,
        bridge_stats.ai_requests_sent,
        health_status.status,
        jid_stats.as_ref().map(|s| s.total_jids).unwrap_or(0),
        jid_stats.as_ref().map(|s| s.active_jids).unwrap_or(0),
        jid_stats
            .as_ref()
            .map(|s| s.total_messages_processed)
            .unwrap_or(0),
        demo_state.messages_processed,
        demo_state.services_healthy
    );

    note("📊 Comprehensive Statistics", stats_info)?;
    Ok(())
}

fn display_quick_start_summary(demo_state: &DemoState) -> Result<(), Box<dyn std::error::Error>> {
    let summary = format!(
        "🚀 Quick Start Summary:\n• Bridge Service: {}\n• JID Authorization: {}\n• Services Health: {}\n• Messages Processed: {}",
        if demo_state.bridge_service.is_some() {
            "✅ Initialized"
        } else {
            "❌ Not initialized"
        },
        if demo_state.jid_authorization.is_some() {
            "✅ Initialized"
        } else {
            "❌ Not initialized"
        },
        if demo_state.services_healthy {
            "✅ Healthy"
        } else {
            "⚠️ Issues detected"
        },
        demo_state.messages_processed
    );

    note("🚀 Quick Start Complete", summary)?;
    Ok(())
}

async fn cleanup_demo(demo_state: &DemoState) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🧹 Cleaning up demo resources...")?;

    // Shutdown JID authorization service
    if let Some(jid_auth) = &demo_state.jid_authorization {
        match jid_auth.shutdown().await {
            Ok(()) => log::success("✅ JID authorization service shutdown complete")?,
            Err(e) => log::warning(format!("⚠️ JID service shutdown error: {}", e))?,
        }
    }

    // Additional cleanup would go here for other services
    log::success("🧹 Demo cleanup completed")?;
    Ok(())
}
