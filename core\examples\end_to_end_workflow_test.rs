/*!
# 🔄 End-to-End WhatsApp Message Processing Workflow Test

This comprehensive test verifies the complete WhatsApp message processing workflow
in the Wellbot Bridge Service, including:

1. **Message Reception**: WebSocket connection to chat-port service
2. **JID Authorization**: Verification of sender authorization
3. **AI Processing**: Integration with Genuis AI service
4. **Response Delivery**: Sending AI responses back via WhatsApp
5. **Error Handling**: Proper handling of unauthorized users and service failures

## Prerequisites
- Chat-port service running on localhost:8081
- Genuis AI service running on localhost:8000
- WebSocket endpoint: ws://localhost:8081/ws
- HTTP API endpoints available

## Usage
```bash
cargo run --example end_to_end_workflow_test --features examples
```
*/

use cliclack::{confirm, input, intro, log, note, outro, select, spinner};
use console::style;
use std::{sync::Arc, time::Duration};
use tokio::time::sleep;
use wellbot_bridge::{
    config::Config,
    services::{
        bridge_service::BridgeService,
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
    types::{IncomingWhatsAppData, SendMessageRequest},
};

/// Test scenarios for the workflow
#[derive(Debug, Clone)]
struct TestScenario {
    name: String,
    description: String,
    jid: String,
    message: String,
    should_be_authorized: bool,
    expected_outcome: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Beautiful intro
    cliclack::clear_screen()?;
    intro(style(" 🔄 End-to-End Workflow Test ").on_cyan().black())?;

    log::info("Testing complete WhatsApp message processing workflow")?;

    // Prerequisites check
    display_prerequisites()?;

    // Configuration setup
    let config = setup_test_configuration().await?;

    // Initialize services
    let (bridge_service, jid_authorization) = initialize_test_services(config).await?;

    // Setup test scenarios
    let test_scenarios = setup_test_scenarios().await?;

    // Add authorized JIDs
    setup_authorized_jids(&jid_authorization, &test_scenarios).await?;

    // Run workflow tests
    run_workflow_tests(&bridge_service, &test_scenarios).await?;

    // Run additional tests
    if confirm("🔬 Run additional integration tests?").interact()? {
        run_integration_tests(&bridge_service).await?;
    }

    // Performance testing
    if confirm("🚀 Run performance tests?").interact()? {
        run_performance_tests(&bridge_service).await?;
    }

    // Display final results
    display_test_results(&bridge_service).await?;

    outro("🎉 End-to-end workflow testing completed successfully!")?;
    Ok(())
}

/// Display prerequisites and service requirements
fn display_prerequisites() -> Result<(), Box<dyn std::error::Error>> {
    note(
        "📋 Prerequisites Check",
        "This test requires the following services:\n\n🤖 Genuis AI Service:\n  • URL: http://localhost:8000\n  • Status: Must be running\n\n💬 Chat-port Service:\n  • WebSocket: ws://localhost:8081/ws\n  • HTTP API: http://localhost:8081/api\n  • Status: Must be running\n\n🔐 Test Requirements:\n  • Authorized WhatsApp JIDs\n  • WebSocket connectivity\n  • AI service accessibility",
    )?;

    if !confirm("🚀 Are all required services running?")
        .initial_value(false)
        .interact()?
    {
        log::warning("Please start the required services before continuing")?;
        std::process::exit(1);
    }

    Ok(())
}

/// Setup test configuration
async fn setup_test_configuration() -> Result<Config, Box<dyn std::error::Error>> {
    log::step("⚙️ Setting up test configuration...")?;

    let config_choice = select("Configuration source:")
        .initial_value("default")
        .item("default", "Default Configuration", "Use built-in defaults")
        .item("env", "Environment Variables", "Load from environment")
        .interact()?;

    let config = match config_choice {
        "env" => Config::from_env().unwrap_or_else(|_| {
            log::warning("Failed to load from environment, using defaults").ok();
            Config::default()
        }),
        _ => Config::default(),
    };

    log::success("✅ Configuration loaded successfully")?;
    Ok(config)
}

/// Initialize test services
async fn initialize_test_services(
    config: Config,
) -> Result<(Arc<BridgeService>, Arc<JidAuthorizationService>), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🔧 Initializing test services...");

    // Initialize JID authorization service
    let jid_config = JidAuthConfig::default();
    let jid_authorization = Arc::new(JidAuthorizationService::new(jid_config).await?);

    // Initialize bridge service
    let bridge_service = Arc::new(BridgeService::new(config, jid_authorization.clone()).await?);

    spinner.stop("✅ Test services initialized");
    log::success("Bridge service and JID authorization ready for testing")?;

    Ok((bridge_service, jid_authorization))
}

/// Setup test scenarios
async fn setup_test_scenarios() -> Result<Vec<TestScenario>, Box<dyn std::error::Error>> {
    log::step("📝 Setting up test scenarios...")?;

    let mut scenarios = vec![
        TestScenario {
            name: "Authorized User - Simple Question".to_string(),
            description: "Test basic AI processing for authorized user".to_string(),
            jid: "<EMAIL>".to_string(),
            message: "What is artificial intelligence?".to_string(),
            should_be_authorized: true,
            expected_outcome: "AI response delivered successfully".to_string(),
        },
        TestScenario {
            name: "Authorized User - Complex Question".to_string(),
            description: "Test complex AI processing".to_string(),
            jid: "<EMAIL>".to_string(),
            message: "Explain quantum computing and its applications in cryptography".to_string(),
            should_be_authorized: true,
            expected_outcome: "Detailed AI response delivered".to_string(),
        },
        TestScenario {
            name: "Unauthorized User".to_string(),
            description: "Test rejection of unauthorized user".to_string(),
            jid: "<EMAIL>".to_string(),
            message: "This should be rejected".to_string(),
            should_be_authorized: false,
            expected_outcome: "Message rejected due to unauthorized JID".to_string(),
        },
    ];

    // Allow user to add custom scenarios
    if confirm("➕ Add custom test scenario?").interact()? {
        let custom_jid: String = input("Enter custom JID")
            .placeholder("<EMAIL>")
            .interact()?;

        let custom_message: String = input("Enter test message")
            .placeholder("Hello, this is a test message")
            .interact()?;

        let is_authorized = confirm("Should this JID be authorized?").interact()?;

        scenarios.push(TestScenario {
            name: "Custom User Test".to_string(),
            description: "User-defined test scenario".to_string(),
            jid: custom_jid,
            message: custom_message,
            should_be_authorized: is_authorized,
            expected_outcome: if is_authorized {
                "AI response expected".to_string()
            } else {
                "Message should be rejected".to_string()
            },
        });
    }

    log::success(format!("✅ {} test scenarios configured", scenarios.len()))?;
    Ok(scenarios)
}

/// Setup authorized JIDs for testing
async fn setup_authorized_jids(
    jid_authorization: &Arc<JidAuthorizationService>,
    scenarios: &[TestScenario],
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔐 Setting up authorized JIDs...")?;

    for scenario in scenarios {
        if scenario.should_be_authorized {
            jid_authorization
                .add_jid(
                    scenario.jid.clone(),
                    Some(format!("Test User - {}", scenario.name)),
                )
                .await?;
            log::success(format!("✅ Authorized JID: {}", scenario.jid))?;
        }
    }

    let authorized_jids = jid_authorization.get_authorized_jids().await;
    log::info(format!(
        "📋 Total authorized JIDs: {}",
        authorized_jids.len()
    ))?;

    Ok(())
}

/// Run workflow tests
async fn run_workflow_tests(
    bridge_service: &Arc<BridgeService>,
    scenarios: &[TestScenario],
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔄 Running end-to-end workflow tests...")?;

    for (i, scenario) in scenarios.iter().enumerate() {
        log::info(format!(
            "📨 Test {}/{}: {}",
            i + 1,
            scenarios.len(),
            scenario.name
        ))?;

        let spinner = spinner();
        spinner.start(format!("Processing: {}", scenario.description));

        // Create test message
        let whatsapp_data = IncomingWhatsAppData {
            from: scenario.jid.clone(),
            message: scenario.message.clone(),
            message_id: format!("test_{}_{}", i, chrono::Utc::now().timestamp()),
            timestamp: chrono::Utc::now(),
        };

        // Process message through bridge service
        let result = bridge_service.process_whatsapp_message(whatsapp_data).await;

        match result {
            Ok(()) => {
                if scenario.should_be_authorized {
                    spinner.stop(format!(
                        "✅ Test {}: SUCCESS - {}",
                        i + 1,
                        scenario.expected_outcome
                    ));
                    log::success(format!(
                        "Message processed successfully for {}",
                        scenario.jid
                    ))?;
                } else {
                    spinner.stop(format!("⚠️ Test {}: UNEXPECTED SUCCESS", i + 1));
                    log::warning("Message was processed but should have been rejected")?;
                }
            }
            Err(e) => {
                if !scenario.should_be_authorized {
                    spinner.stop(format!(
                        "✅ Test {}: SUCCESS - Message correctly rejected",
                        i + 1
                    ));
                    log::success(format!("Unauthorized message correctly rejected: {}", e))?;
                } else {
                    spinner.stop(format!("❌ Test {}: FAILED", i + 1));
                    log::error(format!("Authorized message failed: {}", e))?;
                }
            }
        }

        // Brief pause between tests
        sleep(Duration::from_millis(1000)).await;
    }

    log::success("🎉 Workflow tests completed")?;
    Ok(())
}

/// Run additional integration tests
async fn run_integration_tests(
    bridge_service: &Arc<BridgeService>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🔬 Running additional integration tests...")?;

    // Test 1: Health check
    log::info("🏥 Testing health check functionality...")?;
    match bridge_service.health_check().await {
        Ok(is_healthy) => {
            if is_healthy {
                log::success("✅ Health check passed")?;
            } else {
                log::warning("⚠️ Health check indicates issues")?;
            }
        }
        Err(e) => {
            log::error(format!("❌ Health check failed: {}", e))?;
        }
    }

    // Test 2: Statistics
    log::info("📊 Testing statistics collection...")?;
    let stats = bridge_service.get_stats().await;
    log::info(format!(
        "📈 Current stats - Messages: {}, Unauthorized: {}, AI Requests: {}",
        stats.messages_processed, stats.messages_unauthorized, stats.ai_requests_sent
    ))?;

    log::success("✅ Integration tests completed")?;
    Ok(())
}

/// Run performance tests
async fn run_performance_tests(
    bridge_service: &Arc<BridgeService>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("🚀 Running performance tests...")?;

    let test_jid = "<EMAIL>";

    // Add performance test JID
    // Note: In a real test, we'd need access to the JID authorization service
    log::info("Setting up performance test JID...")?;

    let concurrent_messages = select("Select concurrent message count:")
        .initial_value(5)
        .item(5, "5 messages", "Light load test")
        .item(10, "10 messages", "Medium load test")
        .item(20, "20 messages", "Heavy load test")
        .interact()?;

    log::info(format!(
        "🔄 Sending {} concurrent messages...",
        concurrent_messages
    ))?;

    let start_time = std::time::Instant::now();
    let mut handles = Vec::new();

    for i in 0..concurrent_messages {
        let bridge_service = bridge_service.clone();
        let test_message = IncomingWhatsAppData {
            from: test_jid.to_string(),
            message: format!("Performance test message #{}", i + 1),
            message_id: format!(
                "perf_test_{}_{}",
                i,
                chrono::Utc::now().timestamp_nanos_opt().unwrap()
            ),
            timestamp: chrono::Utc::now(),
        };

        let handle =
            tokio::spawn(
                async move { bridge_service.process_whatsapp_message(test_message).await },
            );

        handles.push(handle);
    }

    // Wait for all messages to complete
    let mut successful = 0;
    let mut failed = 0;

    for handle in handles {
        match handle.await {
            Ok(Ok(())) => successful += 1,
            Ok(Err(_)) => failed += 1,
            Err(_) => failed += 1,
        }
    }

    let elapsed = start_time.elapsed();
    let messages_per_second = concurrent_messages as f64 / elapsed.as_secs_f64();

    log::info(format!(
        "📊 Performance Results:\n• Total Messages: {}\n• Successful: {}\n• Failed: {}\n• Duration: {:.2}s\n• Messages/sec: {:.2}",
        concurrent_messages,
        successful,
        failed,
        elapsed.as_secs_f64(),
        messages_per_second
    ))?;

    if successful > 0 {
        log::success("✅ Performance test completed")?;
    } else {
        log::warning("⚠️ Performance test had issues")?;
    }

    Ok(())
}

/// Display final test results
async fn display_test_results(
    bridge_service: &Arc<BridgeService>,
) -> Result<(), Box<dyn std::error::Error>> {
    log::step("📊 Displaying final test results...")?;

    let stats = bridge_service.get_stats().await;
    let health_status = bridge_service.get_health_status().await;

    let results_summary = match health_status {
        Ok(health) => format!(
            "🔄 End-to-End Workflow Test Results:\n\n📈 Message Processing:\n• Total Messages Processed: {}\n• Authorized Messages: {}\n• Unauthorized Messages: {}\n• AI Requests Sent: {}\n• AI Responses Received: {}\n• Errors Encountered: {}\n\n🏥 System Health:\n• Overall Status: {:?}\n• Components Healthy: {}\n• Critical Components: {}\n\n✅ Workflow Status: {}",
            stats.messages_processed,
            stats.messages_authorized,
            stats.messages_unauthorized,
            stats.ai_requests_sent,
            stats.ai_responses_received,
            stats.errors_encountered,
            health.status,
            health.health_summary.healthy_components,
            health.health_summary.critical_components,
            if stats.messages_processed > 0 {
                "OPERATIONAL"
            } else {
                "NO MESSAGES PROCESSED"
            }
        ),
        Err(e) => format!(
            "🔄 End-to-End Workflow Test Results:\n\n📈 Message Processing:\n• Total Messages Processed: {}\n• Authorized Messages: {}\n• Unauthorized Messages: {}\n• AI Requests Sent: {}\n• AI Responses Received: {}\n• Errors Encountered: {}\n\n❌ Health Check Failed: {}\n\n⚠️ Workflow Status: PARTIAL FUNCTIONALITY",
            stats.messages_processed,
            stats.messages_authorized,
            stats.messages_unauthorized,
            stats.ai_requests_sent,
            stats.ai_responses_received,
            stats.errors_encountered,
            e
        ),
    };

    note("📊 Final Test Results", results_summary)?;

    // Workflow verification
    let workflow_complete = stats.messages_processed > 0
        && stats.ai_requests_sent > 0
        && stats.ai_responses_received > 0;

    if workflow_complete {
        log::success("🎉 Complete end-to-end workflow verified successfully!")?;
        log::info(
            "✅ All components working: Message Reception → JID Authorization → AI Processing → Response Delivery",
        )?;
    } else {
        log::warning("⚠️ Workflow verification incomplete - some components may need attention")?;

        if stats.messages_processed == 0 {
            log::warning("• No messages were processed")?;
        }
        if stats.ai_requests_sent == 0 {
            log::warning("• No AI requests were sent")?;
        }
        if stats.ai_responses_received == 0 {
            log::warning("• No AI responses were received")?;
        }
    }

    Ok(())
}
