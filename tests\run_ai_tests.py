#!/usr/bin/env python3
"""
Simple AI Service Integration test runner for Wellbot Bridge Service

This script provides easy-to-use commands for running AI service integration tests
with different configurations and scenarios.

Usage Examples:
    python run_ai_tests.py                         # Basic AI integration tests
    python run_ai_tests.py --verbose               # Verbose output
    python run_ai_tests.py --save-report           # Save detailed report
    python run_ai_tests.py --timeout 120           # Extended timeout for AI tests
"""

import sys
import subprocess
import argparse
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import requests

        print("✅ Required libraries are available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Install with: pip install requests")
        return False


def check_services_status(
    ai_service_url="http://localhost:8000", bridge_url="http://localhost:3030"
):
    """Check if required services are running"""
    import requests

    services_status = {}

    # Check AI Service
    try:
        response = requests.get(f"{ai_service_url}/api/v1/health", timeout=5)
        services_status["ai"] = response.status_code == 200
        if services_status["ai"]:
            print(f"✅ AI service is running at {ai_service_url}")
        else:
            print(f"⚠️ AI service responded with status {response.status_code}")
    except Exception:
        services_status["ai"] = False
        print(f"❌ AI service not reachable at {ai_service_url}")

    # Check Bridge Service (optional)
    try:
        response = requests.get(f"{bridge_url}/health", timeout=5)
        services_status["bridge"] = response.status_code == 200
        if services_status["bridge"]:
            print(f"✅ Bridge service is running at {bridge_url}")
        else:
            print(f"⚠️ Bridge service responded with status {response.status_code}")
    except Exception:
        services_status["bridge"] = False
        print(
            f"⚠️ Bridge service not reachable at {bridge_url} (tests will skip bridge features)"
        )

    # Check minimum requirements
    if not services_status["ai"]:
        print("\n❌ AI service is not available:")
        print("  - Start AI service: cd genuis && python main.py")
        return False

    return True


def run_ai_integration_tests(args):
    """Run AI service integration tests"""
    cmd = [sys.executable, "ai_service_integration_tests.py"]

    if args.ai_url:
        cmd.extend(["--ai-url", args.ai_url])
    if args.bridge_url:
        cmd.extend(["--bridge-url", args.bridge_url])
    if args.timeout:
        cmd.extend(["--timeout", str(args.timeout)])
    if args.verbose:
        cmd.append("--verbose")
    if args.save_report:
        cmd.append("--save-report")

    print(f"🚀 Running AI service integration tests: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    parser = argparse.ArgumentParser(
        description="Wellbot Bridge AI Service Integration Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_ai_tests.py                         # Basic AI integration tests
  python run_ai_tests.py --verbose               # Verbose output
  python run_ai_tests.py --save-report           # Save detailed report
  python run_ai_tests.py --timeout 120           # Extended timeout for AI tests
  
  # Custom service URLs
  python run_ai_tests.py --ai-url http://localhost:8000
  python run_ai_tests.py --bridge-url http://localhost:3030
  
  # Full test with reporting
  python run_ai_tests.py --verbose --save-report --timeout 120
        """,
    )

    parser.add_argument(
        "--ai-url",
        default="http://localhost:8000",
        help="AI service URL (default: http://localhost:8000)",
    )
    parser.add_argument(
        "--bridge-url",
        default="http://localhost:3030",
        help="Bridge service URL (default: http://localhost:3030)",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=60.0,
        help="Request timeout in seconds (default: 60.0)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--save-report", action="store_true", help="Save detailed report to JSON file"
    )

    # Additional options
    parser.add_argument(
        "--skip-checks", action="store_true", help="Skip dependency and service checks"
    )

    args = parser.parse_args()

    print("🤖 Wellbot Bridge AI Service Integration Test Runner")
    print("=" * 58)

    # Check dependencies and service status
    if not args.skip_checks:
        if not check_dependencies():
            return 1

        if not check_services_status(args.ai_url, args.bridge_url):
            return 1

    # Run AI service integration tests
    try:
        result = run_ai_integration_tests(args)
        return result.returncode

    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
