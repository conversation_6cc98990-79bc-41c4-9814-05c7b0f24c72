# Wellbot Bridge Health Endpoint Testing

Comprehensive Python-based testing suite for the Wellbot Bridge Service health endpoints using the `requests` library.

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** installed
2. **Wellbot Bridge Service** running at `http://localhost:3030`
3. **Python dependencies** installed

### Installation

```bash
# Install Python dependencies
pip install -r requirements.txt

# Or install just the core dependency
pip install requests
```

### Basic Usage

```bash
# Run basic health endpoint tests
python health_endpoint_tests.py

# Run with verbose output
python health_endpoint_tests.py --verbose

# Run with custom service URL
python health_endpoint_tests.py --base-url http://localhost:8080

# Save detailed report
python health_endpoint_tests.py --save-report
```

### Using the Test Runner

```bash
# Basic tests with dependency checks
python run_health_tests.py

# Load testing
python run_health_tests.py --load-test --concurrent 20 --requests 200

# Stress testing for 30 seconds
python run_health_tests.py --stress-test 30

# Verbose output with report saving
python run_health_tests.py --verbose --save-report
```

## 📋 Test Coverage

The test suite covers all Wellbot Bridge Service health endpoints:

### Core Health Endpoints

| Endpoint | Description | Expected Status | Validation |
|----------|-------------|-----------------|------------|
| `/` | API Documentation | 200 | Service info and endpoints list |
| `/health` | Comprehensive Health Status | 200 | Full service health with components |
| `/health/ready` | Kubernetes Readiness Probe | 200/503 | Service readiness status |
| `/health/live` | Kubernetes Liveness Probe | 200 | Service liveness check |

### Advanced Health Endpoints

| Endpoint | Description | Expected Status | Validation |
|----------|-------------|-----------------|------------|
| `/health/trends` | Health Trends Analysis | 200 | Historical health data |
| `/health/metrics` | Performance Metrics | 200 | System and application metrics |
| `/health/circuit-breakers` | Circuit Breaker Status | 200 | Circuit breaker states |
| `/health/history` | Health Check History | 200 | Historical health records |
| `/health/alerts` | Active Alerts | 200 | Current system alerts |
| `/health/recovery` | Manual Recovery Trigger | 405 | Recovery endpoint (POST) |

## 🧪 Test Types

### 1. Basic Health Tests

Validates all health endpoints for:
- Correct HTTP status codes
- Response structure validation
- Response time monitoring
- Error handling

### 2. Load Testing

Tests service performance under load:
- Configurable concurrent requests
- Total request count
- Success rate analysis
- Response time statistics

### 3. Stress Testing

Continuous testing for specified duration:
- Sustained request load
- Error rate monitoring
- Performance degradation detection
- Service stability validation

## 📊 Test Results

### Sample Output

```
[03:25:29] [INFO] 🚀 Starting Wellbot Bridge Health Endpoint Tests
[03:25:29] [INFO] Target URL: http://localhost:3030
[03:25:29] [INFO] Timeout: 10.0s
[03:25:31] [INFO] ✅ Service is reachable (status: 200)
[03:25:31] [PASS] ✅ API Documentation - 3.0ms
[03:25:32] [PASS] ✅ Basic Health Check - 15.2ms
[03:25:33] [PASS] ✅ Readiness Check - 8.1ms
[03:25:34] [PASS] ✅ Liveness Check - 9.0ms
[03:25:35] [PASS] ✅ Health Trends - 11.1ms
[03:25:36] [PASS] ✅ Performance Metrics - 12.5ms
[03:25:37] [PASS] ✅ Circuit Breaker Status - 17.2ms
[03:25:38] [PASS] ✅ Health History - 8.7ms
[03:25:39] [PASS] ✅ Active Alerts - 10.7ms
[03:25:40] [PASS] ✅ Manual Recovery Trigger - 5.3ms
[03:25:41] [INFO] 🏁 All tests completed in 12.1s
[03:25:41] [INFO] 📋 Test Summary:
[03:25:41] [INFO]    Total: 10, Passed: 10, Failed: 0, Warned: 0
[03:25:41] [INFO]    Success Rate: 100.0%
[03:25:41] [INFO]    Avg Response Time: 10.1ms
```

### Test Report Structure

When using `--save-report`, a detailed JSON report is generated:

```json
{
  "test_execution": {
    "timestamp": "2024-01-01T12:00:00",
    "base_url": "http://localhost:3030",
    "total_tests": 10,
    "passed_tests": 8,
    "failed_tests": 2,
    "warned_tests": 0,
    "success_rate": 80.0
  },
  "performance": {
    "avg_response_time_ms": 15.2,
    "min_response_time_ms": 3.0,
    "max_response_time_ms": 45.1
  },
  "test_details": [
    {
      "name": "Basic Health Check",
      "endpoint": "/health",
      "result": "PASS",
      "status_code": 200,
      "response_time_ms": 15.2,
      "error_message": null
    }
  ]
}
```

## 🔧 Configuration Options

### Command Line Arguments

```bash
--base-url URL          # Service base URL (default: http://localhost:3030)
--timeout SECONDS       # Request timeout (default: 10.0)
--verbose, -v           # Enable verbose output
--save-report           # Save detailed JSON report
--load-test             # Run load test after basic tests
--stress-test SECONDS   # Run stress test for specified duration
--concurrent N          # Concurrent requests for load test (default: 10)
--requests N            # Total requests for load test (default: 100)
```

### Environment Variables

```bash
export WELLBOT_BASE_URL=http://localhost:3030
export WELLBOT_TIMEOUT=10
export WELLBOT_VERBOSE=true
```

## 🐛 Troubleshooting

### Common Issues

1. **Service Unreachable**
   ```
   ❌ Service unreachable: Connection refused
   ```
   - Ensure Wellbot Bridge Service is running
   - Check the correct port (default: 3030)
   - Verify firewall settings

2. **Import Error**
   ```
   ModuleNotFoundError: No module named 'requests'
   ```
   - Install dependencies: `pip install requests`
   - Or use: `pip install -r requirements.txt`

3. **Test Failures**
   - Check service logs for errors
   - Verify service configuration
   - Use `--verbose` for detailed output

### Service Startup

Make sure the Wellbot Bridge Service is running:

```bash
cd core
cargo run --bin wellbot-bridge
```

The service should show:
```
INFO wellbot_bridge: 🏥 Health check server listening on port 3030
```

## 📈 Performance Benchmarks

### Expected Performance

- **Liveness Check**: < 500ms
- **Readiness Check**: < 1000ms  
- **Basic Health Check**: < 5000ms
- **Advanced Endpoints**: < 10000ms

### Load Test Targets

- **Success Rate**: > 95%
- **Concurrent Requests**: 10-50
- **Requests per Second**: > 100
- **Error Rate**: < 5%

## 🤝 Contributing

To add new test cases:

1. Add test case to `get_test_cases()` method
2. Create validation function if needed
3. Update documentation
4. Test with various scenarios

Example new test case:
```python
TestCase(
    name="Custom Endpoint Test",
    endpoint="/health/custom",
    description="Test custom health endpoint",
    validation_func=self.validate_custom_response
)
```
