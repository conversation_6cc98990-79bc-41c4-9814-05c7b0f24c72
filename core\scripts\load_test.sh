#!/bin/bash
# load_test.sh - Comprehensive load testing for Wellbot Bridge Service
# Usage: ./load_test.sh [base_url] [test_profile]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8081}"
TEST_PROFILE="${2:-light}"
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
RESULTS_DIR="load_test_results_${TIMESTAMP}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test profiles
declare -A LIGHT_PROFILE=(
    [requests]=50
    [concurrency]=5
    [duration]=30
    [ramp_up]=10
)

declare -A MEDIUM_PROFILE=(
    [requests]=200
    [concurrency]=20
    [duration]=120
    [ramp_up]=30
)

declare -A HEAVY_PROFILE=(
    [requests]=1000
    [concurrency]=50
    [duration]=300
    [ramp_up]=60
)

declare -A STRESS_PROFILE=(
    [requests]=5000
    [concurrency]=100
    [duration]=600
    [ramp_up]=120
)

# Test endpoints for load testing
LOAD_TEST_ENDPOINTS=(
    "/health"
    "/health/ready"
    "/health/live"
    "/health/metrics"
)

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

setup_results_dir() {
    mkdir -p "$RESULTS_DIR"
    log_info "Results will be saved to: $RESULTS_DIR"
}

get_profile_config() {
    local profile="$1"
    case "$profile" in
        "light")
            echo "${LIGHT_PROFILE[requests]} ${LIGHT_PROFILE[concurrency]} ${LIGHT_PROFILE[duration]} ${LIGHT_PROFILE[ramp_up]}"
            ;;
        "medium")
            echo "${MEDIUM_PROFILE[requests]} ${MEDIUM_PROFILE[concurrency]} ${MEDIUM_PROFILE[duration]} ${MEDIUM_PROFILE[ramp_up]}"
            ;;
        "heavy")
            echo "${HEAVY_PROFILE[requests]} ${HEAVY_PROFILE[concurrency]} ${HEAVY_PROFILE[duration]} ${HEAVY_PROFILE[ramp_up]}"
            ;;
        "stress")
            echo "${STRESS_PROFILE[requests]} ${STRESS_PROFILE[concurrency]} ${STRESS_PROFILE[duration]} ${STRESS_PROFILE[ramp_up]}"
            ;;
        *)
            log_error "Unknown test profile: $profile"
            exit 1
            ;;
    esac
}

run_apache_bench_test() {
    local endpoint="$1"
    local requests="$2"
    local concurrency="$3"
    local output_file="$4"
    
    log_info "Running Apache Bench test on $endpoint"
    log_info "Requests: $requests, Concurrency: $concurrency"
    
    if command -v ab >/dev/null 2>&1; then
        ab -n "$requests" -c "$concurrency" -g "$output_file.gnuplot" \
           "$BASE_URL$endpoint" > "$output_file" 2>&1
        
        if [ $? -eq 0 ]; then
            log_success "Apache Bench test completed for $endpoint"
            return 0
        else
            log_error "Apache Bench test failed for $endpoint"
            return 1
        fi
    else
        log_warning "Apache Bench (ab) not available"
        return 1
    fi
}

run_curl_load_test() {
    local endpoint="$1"
    local requests="$2"
    local concurrency="$3"
    local output_file="$4"
    
    log_info "Running curl-based load test on $endpoint"
    log_info "Requests: $requests, Concurrency: $concurrency"
    
    local pids=()
    local start_time=$(date +%s)
    
    # Create output file
    echo "timestamp,response_time,http_code,size" > "$output_file"
    
    # Launch concurrent curl processes
    for ((i=1; i<=concurrency; i++)); do
        {
            local requests_per_process=$((requests / concurrency))
            for ((j=1; j<=requests_per_process; j++)); do
                local request_start=$(date +%s.%N)
                local response=$(curl -s -w "%{http_code}:%{time_total}:%{size_download}" \
                                     --max-time 30 "$BASE_URL$endpoint" 2>/dev/null)
                local request_end=$(date +%s.%N)
                
                if [ $? -eq 0 ]; then
                    local http_code time_total size_download
                    IFS=':' read -r http_code time_total size_download <<< "$response"
                    echo "$request_start,$time_total,$http_code,$size_download" >> "$output_file"
                fi
            done
        } &
        pids+=($!)
    done
    
    # Wait for all processes to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    log_success "Curl load test completed for $endpoint in ${total_time}s"
}

analyze_results() {
    local results_file="$1"
    local endpoint="$2"
    
    if [ ! -f "$results_file" ]; then
        log_warning "Results file not found: $results_file"
        return 1
    fi
    
    log_info "Analyzing results for $endpoint..."
    
    if command -v ab >/dev/null 2>&1 && grep -q "Requests per second" "$results_file"; then
        # Apache Bench results
        local rps=$(grep "Requests per second" "$results_file" | awk '{print $4}')
        local mean_time=$(grep "Time per request" "$results_file" | head -1 | awk '{print $4}')
        local failed_requests=$(grep "Failed requests" "$results_file" | awk '{print $3}')
        local total_requests=$(grep "Complete requests" "$results_file" | awk '{print $3}')
        
        echo "=== Results for $endpoint ===" >> "$RESULTS_DIR/summary.txt"
        echo "Requests per second: $rps" >> "$RESULTS_DIR/summary.txt"
        echo "Mean time per request: ${mean_time}ms" >> "$RESULTS_DIR/summary.txt"
        echo "Failed requests: $failed_requests" >> "$RESULTS_DIR/summary.txt"
        echo "Total requests: $total_requests" >> "$RESULTS_DIR/summary.txt"
        echo "" >> "$RESULTS_DIR/summary.txt"
        
        log_success "RPS: $rps, Mean time: ${mean_time}ms, Failed: $failed_requests/$total_requests"
    else
        # Curl-based results
        if [ -f "$results_file" ] && [ -s "$results_file" ]; then
            local total_requests=$(tail -n +2 "$results_file" | wc -l)
            local successful_requests=$(tail -n +2 "$results_file" | awk -F',' '$3 == 200' | wc -l)
            local failed_requests=$((total_requests - successful_requests))
            local avg_response_time=$(tail -n +2 "$results_file" | awk -F',' '{sum+=$2} END {print sum/NR}')
            
            echo "=== Results for $endpoint ===" >> "$RESULTS_DIR/summary.txt"
            echo "Total requests: $total_requests" >> "$RESULTS_DIR/summary.txt"
            echo "Successful requests: $successful_requests" >> "$RESULTS_DIR/summary.txt"
            echo "Failed requests: $failed_requests" >> "$RESULTS_DIR/summary.txt"
            echo "Average response time: ${avg_response_time}s" >> "$RESULTS_DIR/summary.txt"
            echo "" >> "$RESULTS_DIR/summary.txt"
            
            log_success "Total: $total_requests, Success: $successful_requests, Failed: $failed_requests, Avg time: ${avg_response_time}s"
        fi
    fi
}

monitor_system_resources() {
    local duration="$1"
    local output_file="$RESULTS_DIR/system_resources.log"
    
    log_info "Monitoring system resources for ${duration}s..."
    
    {
        echo "timestamp,cpu_percent,memory_percent,disk_io,network_io"
        local end_time=$(($(date +%s) + duration))
        
        while [ $(date +%s) -lt $end_time ]; do
            local timestamp=$(date +%s)
            local cpu_percent=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
            local memory_percent=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
            
            echo "$timestamp,$cpu_percent,$memory_percent,0,0"
            sleep 5
        done
    } > "$output_file" &
    
    local monitor_pid=$!
    echo "$monitor_pid" > "$RESULTS_DIR/monitor.pid"
}

stop_monitoring() {
    if [ -f "$RESULTS_DIR/monitor.pid" ]; then
        local monitor_pid=$(cat "$RESULTS_DIR/monitor.pid")
        if kill -0 "$monitor_pid" 2>/dev/null; then
            kill "$monitor_pid"
            log_info "System monitoring stopped"
        fi
        rm -f "$RESULTS_DIR/monitor.pid"
    fi
}

connectivity_test() {
    log_info "Testing connectivity to $BASE_URL..."
    
    if curl -s --max-time 5 "$BASE_URL/health" >/dev/null 2>&1; then
        log_success "Service is reachable"
        return 0
    else
        log_error "Service is not reachable at $BASE_URL"
        return 1
    fi
}

warmup_test() {
    log_info "Running warmup requests..."
    
    for endpoint in "${LOAD_TEST_ENDPOINTS[@]}"; do
        for ((i=1; i<=5; i++)); do
            curl -s --max-time 10 "$BASE_URL$endpoint" >/dev/null 2>&1
        done
    done
    
    log_success "Warmup completed"
}

generate_report() {
    local report_file="$RESULTS_DIR/load_test_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Load Test Report - $TIMESTAMP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .results { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Load Test Report</h1>
        <p><strong>Timestamp:</strong> $TIMESTAMP</p>
        <p><strong>Base URL:</strong> $BASE_URL</p>
        <p><strong>Test Profile:</strong> $TEST_PROFILE</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="results">
            <pre>$(cat "$RESULTS_DIR/summary.txt" 2>/dev/null || echo "No summary available")</pre>
        </div>
    </div>
    
    <div class="section">
        <h2>System Resources</h2>
        <div class="results">
            <p>System resource monitoring data saved to: system_resources.log</p>
        </div>
    </div>
</body>
</html>
EOF
    
    log_success "HTML report generated: $report_file"
}

# Main execution
main() {
    echo "======================================"
    echo "Wellbot Bridge Load Test Suite"
    echo "======================================"
    echo "Timestamp: $(date)"
    echo "Base URL: $BASE_URL"
    echo "Test Profile: $TEST_PROFILE"
    echo "======================================"
    echo ""
    
    # Setup
    setup_results_dir
    
    # Test connectivity
    if ! connectivity_test; then
        log_error "Cannot reach service, aborting tests"
        exit 1
    fi
    
    # Get profile configuration
    local config
    config=$(get_profile_config "$TEST_PROFILE")
    read -r requests concurrency duration ramp_up <<< "$config"
    
    log_info "Test configuration: $requests requests, $concurrency concurrency, ${duration}s duration"
    
    # Warmup
    warmup_test
    
    # Start system monitoring
    monitor_system_resources "$duration"
    
    # Run load tests for each endpoint
    for endpoint in "${LOAD_TEST_ENDPOINTS[@]}"; do
        local output_file="$RESULTS_DIR/$(echo "$endpoint" | sed 's/\//_/g')_results.txt"
        
        if command -v ab >/dev/null 2>&1; then
            run_apache_bench_test "$endpoint" "$requests" "$concurrency" "$output_file"
        else
            run_curl_load_test "$endpoint" "$requests" "$concurrency" "$output_file.csv"
        fi
        
        analyze_results "$output_file" "$endpoint"
        
        # Brief pause between endpoint tests
        sleep 2
    done
    
    # Stop monitoring
    stop_monitoring
    
    # Generate report
    generate_report
    
    log_success "Load testing completed. Results saved to: $RESULTS_DIR"
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [base_url] [test_profile]

Arguments:
  base_url      Base URL of the service (default: http://localhost:8081)
  test_profile  Test profile to use (default: light)

Test Profiles:
  light   - 50 requests, 5 concurrency, 30s duration
  medium  - 200 requests, 20 concurrency, 120s duration  
  heavy   - 1000 requests, 50 concurrency, 300s duration
  stress  - 5000 requests, 100 concurrency, 600s duration

Examples:
  $0                                    # Light load test on localhost
  $0 http://localhost:8081 medium       # Medium load test
  $0 https://api.example.com heavy      # Heavy load test on production

Requirements:
  - curl (required)
  - ab (Apache Bench, optional but recommended)
  - Basic Unix tools (awk, grep, etc.)
EOF
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        exit 1
    fi
    
    if ! command -v ab >/dev/null 2>&1; then
        log_warning "Apache Bench (ab) not found - using curl-based load testing"
    fi
}

# Script entry point
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# Trap to ensure cleanup
trap 'stop_monitoring; exit 1' INT TERM

check_dependencies
main
