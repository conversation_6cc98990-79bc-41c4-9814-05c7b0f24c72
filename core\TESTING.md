# Wellbot Bridge Service - Manual Testing Guide

## Overview

This document provides comprehensive manual testing procedures for the Wellbot Bridge Service. Each test includes step-by-step instructions, expected results, and validation criteria.

## Prerequisites

### Environment Setup

- Rust 1.70+ installed
- Docker (optional, for containerized testing)
- HTTP client (curl, Postman, or similar)
- Text editor for configuration files

### Required Environment Variables

```bash
# Core Configuration
RUST_LOG=debug
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Service URLs
AI_SERVICE_BASE_URL=http://localhost:8080
CHAT_PORT_BASE_URL=ws://localhost:3000

# Health Configuration
HEALTH_PORT=8081
HEALTH_CHECK_INTERVAL_SECS=30
HEALTH_HISTORY_RETENTION_HOURS=24

# Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT_SECS=60
CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3

# Alerting Configuration
ALERT_WEBHOOK_URL=http://localhost:9000/webhook
ALERT_EMAIL_ENABLED=false
ALERT_MIN_SEVERITY=warning
```

## Test Categories

### 1. Service Startup and Configuration Tests

#### Test 1.1: Basic Service Startup

**Objective**: Verify the service starts correctly with default configuration

**Steps**:

1. Navigate to the core directory: `cd core`
2. Run the service: `cargo run --bin wellbot-bridge`
3. Observe the startup logs

**Expected Results**:

- Service starts without errors
- Logging initialization message appears
- Configuration loaded successfully
- Health check server starts on configured port
- Bridge service initializes successfully

**Validation Criteria**:

- ✅ No error messages in logs
- ✅ Health server listening message appears
- ✅ Service version and package info displayed
- ✅ All components initialized successfully

#### Test 1.2: Configuration Validation

**Objective**: Verify configuration validation works correctly

**Steps**:

1. Set invalid health port: `export HEALTH_PORT=0`
2. Run the service: `cargo run --bin wellbot-bridge`
3. Observe the error handling

**Expected Results**:

- Service fails to start with clear error message
- Configuration validation error displayed
- Graceful error handling without panic

**Validation Criteria**:

- ✅ Clear error message about invalid port
- ✅ Service exits gracefully
- ✅ No stack traces or panics

### 2. Health Check API Tests

#### Test 2.1: Basic Health Check

**Objective**: Verify the main health endpoint returns comprehensive status

**Steps**:

1. Start the service with default configuration
2. Wait for service to fully initialize (30 seconds)
3. Make HTTP request: `curl -v http://localhost:8081/health`
4. Examine the response

**Expected Results**:

- HTTP 200 OK status
- JSON response with service health information
- All components show appropriate status
- Response time under 5 seconds

**Validation Criteria**:

- ✅ Response contains `status`, `timestamp`, `components`
- ✅ Each component has `status`, `last_check`, `response_time_ms`
- ✅ System metrics included (memory, CPU, disk usage)
- ✅ Uptime information present

#### Test 2.2: Readiness Check

**Objective**: Verify Kubernetes-style readiness endpoint

**Steps**:

1. Start the service
2. Make request: `curl -v http://localhost:8081/health/ready`
3. Check response status and headers

**Expected Results**:

- HTTP 200 OK when service is ready
- HTTP 503 Service Unavailable when not ready
- Minimal response body
- Fast response time (< 1 second)

**Validation Criteria**:

- ✅ Correct HTTP status codes
- ✅ Response time under 1 second
- ✅ Appropriate headers set

#### Test 2.3: Liveness Check

**Objective**: Verify Kubernetes-style liveness endpoint

**Steps**:

1. Start the service
2. Make request: `curl -v http://localhost:8081/health/live`
3. Verify response

**Expected Results**:

- Always returns HTTP 200 OK
- Minimal response
- Very fast response time (< 500ms)

**Validation Criteria**:

- ✅ Always returns 200 OK
- ✅ Response time under 500ms
- ✅ Service responds even under load

### 3. Advanced Health Monitoring Tests

#### Test 3.1: Health Trends Analysis

**Objective**: Verify health trends endpoint provides meaningful data

**Steps**:

1. Start service and let it run for 10 minutes
2. Make request: `curl http://localhost:8081/health/trends`
3. Analyze the trends data

**Expected Results**:

- JSON response with trend analysis
- Availability trends for each component
- Response time trends
- Error rate analysis

**Validation Criteria**:

- ✅ Contains `availability_trend`, `response_time_trend`, `error_rate_trend`
- ✅ Trend values are realistic percentages
- ✅ Data covers appropriate time window

#### Test 3.2: Performance Metrics

**Objective**: Verify performance metrics collection and reporting

**Steps**:

1. Start service and generate some load
2. Make request: `curl http://localhost:8081/health/metrics`
3. Examine metrics data

**Expected Results**:

- Comprehensive performance metrics
- System resource usage statistics
- Component-specific metrics
- Historical data points

**Validation Criteria**:

- ✅ Memory, CPU, disk usage metrics present
- ✅ Response time statistics included
- ✅ Error counts and rates available
- ✅ Uptime and availability metrics

### 4. Circuit Breaker Tests

#### Test 4.1: Circuit Breaker Status

**Objective**: Verify circuit breaker monitoring and reporting

**Steps**:

1. Start the service
2. Make request: `curl http://localhost:8081/health/circuit-breakers`
3. Examine circuit breaker states

**Expected Results**:

- JSON response with circuit breaker status for each component
- State information (CLOSED, OPEN, HALF_OPEN)
- Failure counts and thresholds
- Recovery timing information

**Validation Criteria**:

- ✅ All components have circuit breaker status
- ✅ States are valid enum values
- ✅ Failure counts and thresholds are numbers
- ✅ Timestamps are valid ISO format

#### Test 4.2: Circuit Breaker Triggering

**Objective**: Verify circuit breakers open when failure threshold is reached

**Steps**:

1. Configure low failure threshold: `CIRCUIT_BREAKER_FAILURE_THRESHOLD=2`
2. Start service with unreachable AI service URL
3. Wait for health checks to fail repeatedly
4. Check circuit breaker status

**Expected Results**:

- AI service circuit breaker opens after threshold failures
- Health status reflects circuit breaker state
- Service continues operating with degraded functionality

**Validation Criteria**:

- ✅ Circuit breaker state changes to OPEN
- ✅ Failure count reaches threshold
- ✅ Service remains responsive
- ✅ Other components unaffected

### 5. Recovery and Resilience Tests

#### Test 5.1: Manual Recovery Trigger

**Objective**: Verify manual recovery functionality

**Steps**:

1. Start service with some components in failed state
2. Make request: `curl -X GET http://localhost:8081/health/recovery`
3. Monitor recovery process

**Expected Results**:

- Recovery process initiates for failed components
- JSON response with recovery report
- Components attempt to reconnect/recover
- Status updates reflect recovery attempts

**Validation Criteria**:

- ✅ Recovery report contains attempted components
- ✅ Successful recoveries listed
- ✅ Failed recovery attempts noted
- ✅ Timestamps for recovery actions

#### Test 5.2: Automatic Recovery

**Objective**: Verify automatic recovery mechanisms

**Steps**:

1. Start service with working dependencies
2. Stop a dependency service (simulate failure)
3. Wait for circuit breaker to open
4. Restart the dependency service
5. Wait for automatic recovery

**Expected Results**:

- Circuit breaker eventually transitions to HALF_OPEN
- Successful health checks close the circuit breaker
- Component status returns to healthy
- Service functionality restored

**Validation Criteria**:

- ✅ Circuit breaker state transitions correctly
- ✅ Health status updates appropriately
- ✅ Recovery happens within expected timeframe
- ✅ No manual intervention required

### 6. Alerting and Notification Tests

#### Test 6.1: Health History Tracking

**Objective**: Verify health check history is maintained

**Steps**:

1. Start service and let it run for 1 hour
2. Make request: `curl http://localhost:8081/health/history`
3. Examine historical data

**Expected Results**:

- Array of historical health check results
- Chronological ordering
- Appropriate data retention
- Complete health snapshots

**Validation Criteria**:

- ✅ History contains multiple entries
- ✅ Entries are chronologically ordered
- ✅ Each entry has complete health data
- ✅ Data retention policy respected

#### Test 6.2: Active Alerts Monitoring

**Objective**: Verify alert generation and tracking

**Steps**:

1. Configure alerting with low thresholds
2. Start service and trigger alert conditions
3. Make request: `curl http://localhost:8081/health/alerts`
4. Examine active alerts

**Expected Results**:

- JSON response with active alerts
- Alert details include severity, component, message
- Alert timestamps and duration
- Alert count summary

**Validation Criteria**:

- ✅ Active alerts listed with details
- ✅ Alert severity levels correct
- ✅ Component identification accurate
- ✅ Timestamps in correct format

### 7. Load and Stress Tests

#### Test 7.1: Concurrent Health Check Requests

**Objective**: Verify service handles multiple simultaneous health checks

**Steps**:

1. Start the service
2. Use a load testing tool or script to make 50 concurrent requests to `/health`
3. Monitor response times and success rates

**Expected Results**:

- All requests return successful responses
- Response times remain reasonable (< 10 seconds)
- No errors or timeouts
- Service remains stable

**Validation Criteria**:

- ✅ 100% success rate for health checks
- ✅ Average response time under 5 seconds
- ✅ No memory leaks or resource exhaustion
- ✅ Service continues normal operation

#### Test 7.2: Extended Runtime Test

**Objective**: Verify service stability over extended periods

**Steps**:

1. Start the service
2. Let it run for 24 hours with periodic health checks
3. Monitor memory usage, response times, and error rates
4. Check for any degradation over time

**Expected Results**:

- Stable memory usage (no memory leaks)
- Consistent response times
- No accumulated errors
- Proper log rotation and cleanup

**Validation Criteria**:

- ✅ Memory usage remains stable
- ✅ Response times don't degrade
- ✅ Error rates remain low
- ✅ Log files managed properly

### 8. Error Handling and Edge Cases

#### Test 8.1: Invalid Request Handling

**Objective**: Verify proper handling of malformed requests

**Steps**:

1. Start the service
2. Make invalid requests to health endpoints
3. Test with various malformed payloads and headers

**Expected Results**:

- Appropriate HTTP error codes (400, 404, 405)
- Clear error messages
- Service remains stable
- No security vulnerabilities exposed

**Validation Criteria**:

- ✅ Correct HTTP status codes returned
- ✅ Error messages are informative but not revealing
- ✅ Service doesn't crash or become unstable
- ✅ No sensitive information leaked

#### Test 8.2: Resource Exhaustion Handling

**Objective**: Verify graceful handling of resource constraints

**Steps**:

1. Start service with limited memory/CPU
2. Generate high load to exhaust resources
3. Monitor service behavior and recovery

**Expected Results**:

- Service degrades gracefully under load
- Health checks may slow but don't fail completely
- Recovery when resources become available
- Appropriate error responses during overload

**Validation Criteria**:

- ✅ No crashes or panics under load
- ✅ Graceful degradation of performance
- ✅ Recovery when load decreases
- ✅ Meaningful error responses

## Test Execution Checklist

### Pre-Test Setup

- [ ] Environment variables configured
- [ ] Dependencies available (if testing integration)
- [ ] Test tools installed and configured
- [ ] Baseline metrics recorded

### During Testing

- [ ] Monitor system resources
- [ ] Record response times
- [ ] Capture error logs
- [ ] Document unexpected behaviors

### Post-Test Validation

- [ ] All expected results achieved
- [ ] No memory leaks detected
- [ ] Log files reviewed for errors
- [ ] Performance metrics within acceptable ranges

## Troubleshooting Common Issues

### Service Won't Start

- Check environment variables are set correctly
- Verify port availability
- Review configuration file syntax
- Check dependency service availability

### Health Checks Failing

- Verify network connectivity to dependencies
- Check service URLs and ports
- Review authentication credentials
- Monitor resource usage

### Performance Issues

- Check system resource availability
- Review log levels (reduce if too verbose)
- Monitor network latency
- Verify database/service performance

## Test Report Template

```markdown
# Test Execution Report

**Date**: [Date]
**Tester**: [Name]
**Version**: [Service Version]
**Environment**: [Test Environment]

## Test Results Summary

- Total Tests: [Number]
- Passed: [Number]
- Failed: [Number]
- Skipped: [Number]

## Failed Tests

[List any failed tests with details]

## Performance Metrics

- Average Response Time: [Time]
- Memory Usage: [Usage]
- CPU Usage: [Usage]

## Recommendations

[Any recommendations for improvements]
```

## Advanced Testing Scenarios

### 9. Integration Testing

#### Test 9.1: End-to-End Message Processing

**Objective**: Verify complete message flow through the bridge service

**Steps**:

1. Start all required services (AI service, Chat-port, Bridge)
2. Send a test message through the WebSocket connection
3. Monitor message processing through all components
4. Verify AI response generation and delivery

**Expected Results**:

- Message received and processed successfully
- AI service generates appropriate response
- Response delivered back through WebSocket
- All health checks remain green during processing

**Validation Criteria**:

- ✅ Message processing completes within SLA
- ✅ No errors in any component logs
- ✅ Response quality meets expectations
- ✅ Health metrics show normal operation

#### Test 9.2: Dependency Failure Simulation

**Objective**: Test service behavior when dependencies fail

**Steps**:

1. Start bridge service with all dependencies running
2. Systematically stop each dependency service
3. Monitor bridge service behavior and health status
4. Restart dependencies and verify recovery

**Expected Results**:

- Circuit breakers activate for failed dependencies
- Service continues operating with degraded functionality
- Health status accurately reflects component states
- Automatic recovery when dependencies return

**Validation Criteria**:

- ✅ Circuit breakers trigger appropriately
- ✅ Service remains responsive during failures
- ✅ Recovery happens automatically
- ✅ No data loss during failure/recovery

### 10. Security Testing

#### Test 10.1: Health Endpoint Security

**Objective**: Verify health endpoints don't expose sensitive information

**Steps**:

1. Start the service
2. Make requests to all health endpoints
3. Examine responses for sensitive data
4. Test with various user agents and headers

**Expected Results**:

- No sensitive configuration data exposed
- No internal system details revealed
- Appropriate CORS headers set
- No authentication bypass possible

**Validation Criteria**:

- ✅ No passwords or tokens in responses
- ✅ No internal IP addresses or paths exposed
- ✅ CORS policy properly configured
- ✅ Rate limiting prevents abuse

#### Test 10.2: Input Validation Testing

**Objective**: Verify proper input validation and sanitization

**Steps**:

1. Test health endpoints with malicious payloads
2. Attempt SQL injection, XSS, and other attacks
3. Test with oversized requests and malformed data
4. Monitor for any security vulnerabilities

**Expected Results**:

- All malicious inputs properly rejected
- No code injection vulnerabilities
- Appropriate error responses
- Service remains secure and stable

**Validation Criteria**:

- ✅ No successful injection attacks
- ✅ Input validation prevents malformed data
- ✅ Error messages don't reveal system details
- ✅ No buffer overflows or crashes

### 11. Performance Benchmarking

#### Test 11.1: Baseline Performance Measurement

**Objective**: Establish performance baselines for all endpoints

**Steps**:

1. Start service in clean environment
2. Measure response times for each endpoint under no load
3. Record memory and CPU usage baselines
4. Document baseline metrics for future comparison

**Expected Results**:

- Consistent response times across multiple runs
- Stable resource usage patterns
- Documented performance baselines
- Reproducible test conditions

**Validation Criteria**:

- ✅ Response times within acceptable ranges
- ✅ Resource usage is reasonable
- ✅ Performance is consistent
- ✅ Baselines documented for regression testing

#### Test 11.2: Scalability Testing

**Objective**: Determine service scalability limits

**Steps**:

1. Gradually increase concurrent request load
2. Monitor response times and error rates
3. Identify breaking points and bottlenecks
4. Test horizontal scaling capabilities

**Expected Results**:

- Graceful performance degradation under load
- Clear identification of bottlenecks
- Scalability limits documented
- Recovery after load reduction

**Validation Criteria**:

- ✅ Performance degrades gracefully
- ✅ No sudden failures at load thresholds
- ✅ Bottlenecks identified and documented
- ✅ Service recovers after load reduction

## Automated Testing Scripts

### Health Check Automation Script

```bash
#!/bin/bash
# health_check_test.sh - Automated health endpoint testing

BASE_URL="http://localhost:8081"
ENDPOINTS=("/health" "/health/ready" "/health/live" "/health/trends" "/health/metrics")

echo "Starting automated health check tests..."

for endpoint in "${ENDPOINTS[@]}"; do
    echo "Testing $endpoint..."

    response=$(curl -s -w "%{http_code}:%{time_total}" "$BASE_URL$endpoint")
    http_code=$(echo $response | cut -d: -f1)
    time_total=$(echo $response | cut -d: -f2)

    if [ "$http_code" -eq 200 ]; then
        echo "✅ $endpoint - HTTP $http_code - ${time_total}s"
    else
        echo "❌ $endpoint - HTTP $http_code - ${time_total}s"
    fi
done

echo "Health check tests completed."
```

### Load Testing Script

```bash
#!/bin/bash
# load_test.sh - Simple load testing for health endpoints

BASE_URL="http://localhost:8081"
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=100

echo "Starting load test..."
echo "Concurrent requests: $CONCURRENT_REQUESTS"
echo "Total requests: $TOTAL_REQUESTS"

# Test main health endpoint under load
ab -n $TOTAL_REQUESTS -c $CONCURRENT_REQUESTS "$BASE_URL/health" > load_test_results.txt

echo "Load test completed. Results saved to load_test_results.txt"

# Extract key metrics
echo "=== Load Test Summary ==="
grep "Requests per second" load_test_results.txt
grep "Time per request" load_test_results.txt
grep "Failed requests" load_test_results.txt
```

### Continuous Monitoring Script

```bash
#!/bin/bash
# monitor.sh - Continuous health monitoring

BASE_URL="http://localhost:8081"
INTERVAL=30
LOG_FILE="health_monitor.log"

echo "Starting continuous health monitoring..."
echo "Interval: ${INTERVAL}s"
echo "Log file: $LOG_FILE"

while true; do
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Check main health endpoint
    response=$(curl -s -w "%{http_code}:%{time_total}" "$BASE_URL/health")
    http_code=$(echo $response | cut -d: -f1)
    time_total=$(echo $response | cut -d: -f2)

    # Log result
    echo "$timestamp - HTTP $http_code - ${time_total}s" >> $LOG_FILE

    if [ "$http_code" -ne 200 ]; then
        echo "⚠️  Health check failed at $timestamp - HTTP $http_code"
    fi

    sleep $INTERVAL
done
```

## Test Data and Fixtures

### Sample Configuration Files

#### Minimal Configuration (minimal.env)

```bash
# Minimal configuration for basic testing
HEALTH_PORT=8081
AI_SERVICE_BASE_URL=http://localhost:8080
CHAT_PORT_BASE_URL=ws://localhost:3000
LOG_LEVEL=info
```

#### High-Load Configuration (load.env)

```bash
# Configuration optimized for load testing
HEALTH_PORT=8081
HEALTH_CHECK_INTERVAL_SECS=10
CIRCUIT_BREAKER_FAILURE_THRESHOLD=10
CIRCUIT_BREAKER_RECOVERY_TIMEOUT_SECS=30
LOG_LEVEL=warn
METRICS_COLLECTION_INTERVAL_SECS=5
```

#### Development Configuration (dev.env)

```bash
# Configuration for development and debugging
HEALTH_PORT=8081
LOG_LEVEL=debug
LOG_FORMAT=pretty
HEALTH_CHECK_INTERVAL_SECS=15
CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
ALERT_MIN_SEVERITY=info
```

### Mock Service Setup

#### Mock AI Service (Python)

```python
# mock_ai_service.py - Simple mock AI service for testing
from flask import Flask, jsonify, request
import time
import random

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health():
    # Simulate occasional failures for testing
    if random.random() < 0.1:  # 10% failure rate
        return jsonify({"error": "Service temporarily unavailable"}), 503

    return jsonify({
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0"
    })

@app.route('/chat', methods=['POST'])
def chat():
    # Simulate processing time
    time.sleep(random.uniform(0.1, 2.0))

    data = request.get_json()
    return jsonify({
        "response": f"Mock response to: {data.get('message', 'unknown')}",
        "timestamp": time.time()
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
```

## Regression Testing Checklist

### Before Each Release

- [ ] All manual tests pass
- [ ] Automated test suite runs successfully
- [ ] Performance benchmarks meet requirements
- [ ] Security tests show no vulnerabilities
- [ ] Load tests demonstrate acceptable performance
- [ ] Integration tests with all dependencies pass

### After Configuration Changes

- [ ] Health check endpoints respond correctly
- [ ] Circuit breaker thresholds work as expected
- [ ] Alerting configuration functions properly
- [ ] Logging levels and formats are correct
- [ ] Performance impact is acceptable

### After Code Changes

- [ ] All existing functionality preserved
- [ ] New features work as specified
- [ ] Error handling remains robust
- [ ] Performance hasn't degraded
- [ ] Security posture maintained

## Troubleshooting Guide

### Common Test Failures

#### Health Endpoint Returns 503

**Possible Causes**:

- Dependencies not running
- Circuit breakers in OPEN state
- Resource exhaustion
- Configuration errors

**Resolution Steps**:

1. Check dependency service status
2. Review circuit breaker states
3. Monitor system resources
4. Validate configuration

#### Slow Response Times

**Possible Causes**:

- High system load
- Network latency
- Database performance issues
- Memory pressure

**Resolution Steps**:

1. Monitor system resources
2. Check network connectivity
3. Review database performance
4. Analyze memory usage patterns

#### Test Environment Issues

**Possible Causes**:

- Port conflicts
- Permission issues
- Missing dependencies
- Environment variable conflicts

**Resolution Steps**:

1. Check port availability
2. Verify user permissions
3. Install missing dependencies
4. Review environment configuration

---

**Note**: This comprehensive testing guide should be updated as new features are added to the service. Regular review and updates ensure comprehensive test coverage and maintain testing effectiveness.
